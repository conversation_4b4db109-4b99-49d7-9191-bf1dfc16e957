<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="page-title">数据仪表板</h1>
        <p class="page-description">实时监控企业关键业务指标</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="showSettings = true">
          <el-icon><Setting /></el-icon>
          设置
        </el-button>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card" v-for="metric in keyMetrics" :key="metric.key">
        <div class="metric-icon">
          <el-icon :size="24" :color="metric.color">
            <component :is="metric.icon" />
          </el-icon>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metric.value }}</div>
          <div class="metric-label">{{ metric.label }}</div>
          <div class="metric-change" :class="metric.changeType">
            <el-icon :size="12">
              <component :is="metric.changeType === 'increase' ? 'ArrowUp' : 'ArrowDown'" />
            </el-icon>
            {{ metric.change }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 销售趋势图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>销售趋势</h3>
          <el-select v-model="salesPeriod" size="small" style="width: 120px">
            <el-option label="近7天" value="7d" />
            <el-option label="近30天" value="30d" />
            <el-option label="近90天" value="90d" />
          </el-select>
        </div>
        <div class="chart-content">
          <SalesChart :period="salesPeriod" />
        </div>
      </div>

      <!-- 订单状态分布 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>订单状态分布</h3>
        </div>
        <div class="chart-content">
          <OrderStatusChart />
        </div>
      </div>

      <!-- 部门业绩对比 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>部门业绩对比</h3>
        </div>
        <div class="chart-content">
          <DepartmentChart />
        </div>
      </div>

      <!-- 实时数据流 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>实时数据流</h3>
          <el-tag :type="dataStreamStatus === 'connected' ? 'success' : 'danger'" size="small">
            {{ dataStreamStatus === 'connected' ? '已连接' : '断开连接' }}
          </el-tag>
        </div>
        <div class="chart-content">
          <RealTimeChart />
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="activity-section">
      <div class="section-header">
        <h3>最近活动</h3>
        <el-link type="primary" @click="$router.push('/activity')">查看全部</el-link>
      </div>
      <div class="activity-list">
        <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
          <div class="activity-icon">
            <el-icon :color="activity.iconColor">
              <component :is="activity.icon" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-description">{{ activity.description }}</div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog
      v-model="showSettings"
      title="仪表板设置"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="settings" label-width="120px">
        <el-form-item label="自动刷新">
          <el-switch v-model="settings.autoRefresh" />
        </el-form-item>
        <el-form-item label="刷新间隔">
          <el-select v-model="settings.refreshInterval" :disabled="!settings.autoRefresh">
            <el-option label="30秒" :value="30" />
            <el-option label="1分钟" :value="60" />
            <el-option label="5分钟" :value="300" />
            <el-option label="10分钟" :value="600" />
          </el-select>
        </el-form-item>
        <el-form-item label="显示动画">
          <el-switch v-model="settings.showAnimation" />
        </el-form-item>
        <el-form-item label="主题模式">
          <el-radio-group v-model="settings.theme">
            <el-radio label="light">浅色</el-radio>
            <el-radio label="dark">深色</el-radio>
            <el-radio label="auto">自动</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import SalesChart from '@/components/charts/SalesChart.vue'
import OrderStatusChart from '@/components/charts/OrderStatusChart.vue'
import DepartmentChart from '@/components/charts/DepartmentChart.vue'
import RealTimeChart from '@/components/charts/RealTimeChart.vue'
import { formatTime } from '@/utils/date'

// 响应式数据
const showSettings = ref(false)
const salesPeriod = ref('30d')
const dataStreamStatus = ref<'connected' | 'disconnected'>('connected')

// 设置
const settings = reactive({
  autoRefresh: true,
  refreshInterval: 300,
  showAnimation: true,
  theme: 'light'
})

// 关键指标数据
const keyMetrics = ref([
  {
    key: 'sales',
    label: '今日销售额',
    value: '¥128,456',
    change: '+12.5%',
    changeType: 'increase',
    icon: 'TrendCharts',
    color: '#409EFF'
  },
  {
    key: 'orders',
    label: '待处理订单',
    value: '234',
    change: '-5.2%',
    changeType: 'decrease',
    icon: 'Document',
    color: '#67C23A'
  },
  {
    key: 'users',
    label: '在线用户',
    value: '1,456',
    change: '+8.1%',
    changeType: 'increase',
    icon: 'User',
    color: '#E6A23C'
  },
  {
    key: 'alerts',
    label: '系统告警',
    value: '3',
    change: '+2',
    changeType: 'increase',
    icon: 'Warning',
    color: '#F56C6C'
  }
])

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    title: '新订单创建',
    description: '客户张三创建了订单 #SO2024001',
    time: new Date(Date.now() - 5 * 60 * 1000),
    icon: 'Plus',
    iconColor: '#67C23A'
  },
  {
    id: 2,
    title: '库存预警',
    description: '产品A001库存不足，当前库存：5件',
    time: new Date(Date.now() - 15 * 60 * 1000),
    icon: 'Warning',
    iconColor: '#E6A23C'
  },
  {
    id: 3,
    title: '数据同步完成',
    description: '金蝶云星辰数据同步完成，共同步1,234条记录',
    time: new Date(Date.now() - 30 * 60 * 1000),
    icon: 'Refresh',
    iconColor: '#409EFF'
  },
  {
    id: 4,
    title: '用户登录',
    description: '管理员admin登录系统',
    time: new Date(Date.now() - 45 * 60 * 1000),
    icon: 'User',
    iconColor: '#909399'
  }
])

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 刷新数据
const refreshData = async () => {
  try {
    ElMessage.success('数据刷新成功')
    // TODO: 实际的数据刷新逻辑
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

// 保存设置
const saveSettings = () => {
  localStorage.setItem('dashboardSettings', JSON.stringify(settings))
  ElMessage.success('设置保存成功')
  showSettings.value = false
  
  // 重新设置自动刷新
  setupAutoRefresh()
}

// 设置自动刷新
const setupAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  
  if (settings.autoRefresh) {
    refreshTimer = setInterval(() => {
      refreshData()
    }, settings.refreshInterval * 1000)
  }
}

// 加载设置
const loadSettings = () => {
  const savedSettings = localStorage.getItem('dashboardSettings')
  if (savedSettings) {
    Object.assign(settings, JSON.parse(savedSettings))
  }
}

onMounted(() => {
  loadSettings()
  setupAutoRefresh()
  refreshData()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  
  .header-left {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .page-description {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  
  .metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: rgba(64, 158, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .metric-content {
    flex: 1;
    
    .metric-value {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .metric-label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 8px;
    }
    
    .metric-change {
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      
      &.increase {
        color: #67c23a;
      }
      
      &.decrease {
        color: #f56c6c;
      }
    }
  }
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .chart-header {
    padding: 20px 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }
  
  .chart-content {
    padding: 20px 24px 24px;
    height: 300px;
  }
}

.activity-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }
}

.activity-list {
  .activity-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .activity-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #f5f7fa;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    
    .activity-content {
      flex: 1;
      
      .activity-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .activity-description {
        font-size: 13px;
        color: #606266;
        margin-bottom: 4px;
      }
      
      .activity-time {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    
    .header-right {
      justify-content: flex-end;
    }
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
