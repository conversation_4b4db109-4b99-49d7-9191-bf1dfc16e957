import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化时间
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的时间字符串
 */
export function formatTime(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param date 日期
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: Date | string | number): string {
  return dayjs(date).fromNow()
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string | number, format = 'YYYY-MM-DD'): string {
  return dayjs(date).format(format)
}

/**
 * 获取今天的开始时间
 * @returns 今天的开始时间
 */
export function getStartOfToday(): Date {
  return dayjs().startOf('day').toDate()
}

/**
 * 获取今天的结束时间
 * @returns 今天的结束时间
 */
export function getEndOfToday(): Date {
  return dayjs().endOf('day').toDate()
}

/**
 * 获取本周的开始时间
 * @returns 本周的开始时间
 */
export function getStartOfWeek(): Date {
  return dayjs().startOf('week').toDate()
}

/**
 * 获取本周的结束时间
 * @returns 本周的结束时间
 */
export function getEndOfWeek(): Date {
  return dayjs().endOf('week').toDate()
}

/**
 * 获取本月的开始时间
 * @returns 本月的开始时间
 */
export function getStartOfMonth(): Date {
  return dayjs().startOf('month').toDate()
}

/**
 * 获取本月的结束时间
 * @returns 本月的结束时间
 */
export function getEndOfMonth(): Date {
  return dayjs().endOf('month').toDate()
}

/**
 * 获取指定天数前的日期
 * @param days 天数
 * @returns 指定天数前的日期
 */
export function getDaysAgo(days: number): Date {
  return dayjs().subtract(days, 'day').toDate()
}

/**
 * 获取指定天数后的日期
 * @param days 天数
 * @returns 指定天数后的日期
 */
export function getDaysAfter(days: number): Date {
  return dayjs().add(days, 'day').toDate()
}

/**
 * 判断是否为今天
 * @param date 日期
 * @returns 是否为今天
 */
export function isToday(date: Date | string | number): boolean {
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param date 日期
 * @returns 是否为昨天
 */
export function isYesterday(date: Date | string | number): boolean {
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否为本周
 * @param date 日期
 * @returns 是否为本周
 */
export function isThisWeek(date: Date | string | number): boolean {
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否为本月
 * @param date 日期
 * @returns 是否为本月
 */
export function isThisMonth(date: Date | string | number): boolean {
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 计算两个日期之间的天数差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 天数差
 */
export function getDaysDiff(date1: Date | string | number, date2: Date | string | number): number {
  return dayjs(date1).diff(dayjs(date2), 'day')
}

/**
 * 计算两个日期之间的小时差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 小时差
 */
export function getHoursDiff(date1: Date | string | number, date2: Date | string | number): number {
  return dayjs(date1).diff(dayjs(date2), 'hour')
}

/**
 * 计算两个日期之间的分钟差
 * @param date1 日期1
 * @param date2 日期2
 * @returns 分钟差
 */
export function getMinutesDiff(date1: Date | string | number, date2: Date | string | number): number {
  return dayjs(date1).diff(dayjs(date2), 'minute')
}

/**
 * 获取时间范围的描述
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 时间范围描述
 */
export function getTimeRangeDescription(startDate: Date | string | number, endDate: Date | string | number): string {
  const start = dayjs(startDate)
  const end = dayjs(endDate)
  
  if (start.isSame(end, 'day')) {
    return start.format('YYYY年MM月DD日')
  } else if (start.isSame(end, 'month')) {
    return `${start.format('YYYY年MM月DD日')} - ${end.format('DD日')}`
  } else if (start.isSame(end, 'year')) {
    return `${start.format('MM月DD日')} - ${end.format('MM月DD日')}`
  } else {
    return `${start.format('YYYY年MM月DD日')} - ${end.format('YYYY年MM月DD日')}`
  }
}

/**
 * 智能格式化时间
 * @param date 日期
 * @returns 智能格式化后的时间字符串
 */
export function smartFormatTime(date: Date | string | number): string {
  const now = dayjs()
  const target = dayjs(date)
  const diffMinutes = now.diff(target, 'minute')
  const diffHours = now.diff(target, 'hour')
  const diffDays = now.diff(target, 'day')
  
  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffHours < 24) {
    return `${diffHours}小时前`
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else if (target.isSame(now, 'year')) {
    return target.format('MM-DD HH:mm')
  } else {
    return target.format('YYYY-MM-DD HH:mm')
  }
}
