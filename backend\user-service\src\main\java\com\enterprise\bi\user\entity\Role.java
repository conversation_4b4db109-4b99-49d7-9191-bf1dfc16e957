package com.enterprise.bi.user.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色实体类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "roles")
@EqualsAndHashCode(callSuper = false)
public class Role {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 角色代码
     */
    @Column(name = "code", nullable = false, unique = true, length = 50)
    private String code;
    
    /**
     * 角色名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    /**
     * 角色描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 角色状态：ACTIVE, INACTIVE
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status = "ACTIVE";
    
    /**
     * 是否系统内置角色
     */
    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * 角色权限关联
     */
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RolePermission> rolePermissions;
    
    /**
     * 用户角色关联
     */
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserRole> userRoles;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
