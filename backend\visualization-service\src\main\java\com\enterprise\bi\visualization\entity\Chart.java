package com.enterprise.bi.visualization.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 图表实体类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "charts")
@EqualsAndHashCode(callSuper = false)
public class Chart {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 图表名称
     */
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    /**
     * 图表描述
     */
    @Column(name = "description", length = 1000)
    private String description;
    
    /**
     * 图表类型：LINE, BAR, PIE, SCATTER, AREA, GAUGE, FUNNEL, RADAR
     */
    @Column(name = "type", nullable = false, length = 50)
    private String type;
    
    /**
     * 数据源ID
     */
    @Column(name = "data_source_id")
    private Long dataSourceId;
    
    /**
     * 数据查询SQL
     */
    @Column(name = "query_sql", columnDefinition = "TEXT")
    private String querySql;
    
    /**
     * 图表配置（JSON格式）
     */
    @Column(name = "chart_config", columnDefinition = "TEXT")
    private String chartConfig;
    
    /**
     * 样式配置（JSON格式）
     */
    @Column(name = "style_config", columnDefinition = "TEXT")
    private String styleConfig;
    
    /**
     * 数据刷新间隔（秒）
     */
    @Column(name = "refresh_interval")
    private Integer refreshInterval = 300; // 5分钟
    
    /**
     * 是否启用实时数据
     */
    @Column(name = "is_realtime", nullable = false)
    private Boolean isRealtime = false;
    
    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;
    
    /**
     * 缓存时间（秒）
     */
    @Column(name = "cache_duration")
    private Integer cacheDuration = 300;
    
    /**
     * 最后执行时间
     */
    @Column(name = "last_executed_at")
    private LocalDateTime lastExecutedAt;
    
    /**
     * 执行次数
     */
    @Column(name = "execution_count")
    private Long executionCount = 0L;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
