<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <el-icon size="32" color="#409EFF">
            <DataAnalysis />
          </el-icon>
        </div>
        <h1 class="title">企业数据可视化BI平台</h1>
        <p class="subtitle">统一的企业数据集成与可视化系统</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item v-if="showCaptcha" prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              size="large"
              prefix-icon="Picture"
              clearable
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
              <span v-else>点击获取</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe">
              记住我
            </el-checkbox>
            <el-link type="primary" @click="showForgotPassword = true">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="third-party-login">
        <el-divider>
          <span class="divider-text">其他登录方式</span>
        </el-divider>
        <div class="third-party-buttons">
          <el-button
            circle
            size="large"
            @click="handleThirdPartyLogin('dingtalk')"
          >
            <el-icon size="20">
              <ChatDotRound />
            </el-icon>
          </el-button>
          <el-button
            circle
            size="large"
            @click="handleThirdPartyLogin('wechat')"
          >
            <el-icon size="20">
              <ChatRound />
            </el-icon>
          </el-button>
          <el-button
            circle
            size="large"
            @click="handleThirdPartyLogin('feishu')"
          >
            <el-icon size="20">
              <Message />
            </el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="showForgotPassword"
      title="重置密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="resetFormRef"
        :model="resetForm"
        :rules="resetRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="resetForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="验证方式" prop="type">
          <el-radio-group v-model="resetForm.type">
            <el-radio label="email">邮箱</el-radio>
            <el-radio label="sms">短信</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item 
          :label="resetForm.type === 'email' ? '邮箱' : '手机号'" 
          prop="contact"
        >
          <div class="code-input-container">
            <el-input 
              v-model="resetForm.contact" 
              :placeholder="resetForm.type === 'email' ? '请输入邮箱' : '请输入手机号'"
            />
            <el-button 
              :disabled="codeCountdown > 0"
              @click="sendResetCode"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}s` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <el-input v-model="resetForm.code" placeholder="请输入验证码" />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="resetForm.newPassword" 
            type="password" 
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showForgotPassword = false">取消</el-button>
        <el-button type="primary" :loading="resetLoading" @click="handleResetPassword">
          重置密码
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import type { LoginForm } from '@/types/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()
const resetFormRef = ref<FormInstance>()

// 加载状态
const loading = ref(false)
const resetLoading = ref(false)

// 验证码相关
const showCaptcha = ref(false)
const captchaImage = ref('')
const captchaKey = ref('')

// 忘记密码
const showForgotPassword = ref(false)
const codeCountdown = ref(0)

// 登录表单
const loginForm = reactive<LoginForm>({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false
})

// 重置密码表单
const resetForm = reactive({
  username: '',
  type: 'email',
  contact: '',
  code: '',
  newPassword: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
}

const resetRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    await userStore.login(loginForm)
    
    // 登录成功后跳转
    const redirect = route.query.redirect as string || '/dashboard'
    router.push(redirect)
  } catch (error) {
    console.error('登录失败:', error)
    // 登录失败后显示验证码
    if (!showCaptcha.value) {
      showCaptcha.value = true
      await refreshCaptcha()
    }
  } finally {
    loading.value = false
  }
}

// 第三方登录
const handleThirdPartyLogin = (platform: 'dingtalk' | 'wechat' | 'feishu') => {
  ElMessage.info(`${platform} 登录功能开发中...`)
}

// 刷新验证码
const refreshCaptcha = async () => {
  try {
    const response = await authApi.getCaptcha()
    captchaImage.value = response.data.captcha
    captchaKey.value = response.data.key
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 发送重置验证码
const sendResetCode = async () => {
  if (!resetForm.contact) {
    ElMessage.warning('请先输入联系方式')
    return
  }
  
  try {
    if (resetForm.type === 'email') {
      await authApi.sendEmailCode(resetForm.contact)
    } else {
      await authApi.sendSmsCode(resetForm.contact)
    }
    
    ElMessage.success('验证码已发送')
    
    // 开始倒计时
    codeCountdown.value = 60
    const timer = setInterval(() => {
      codeCountdown.value--
      if (codeCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (error) {
    console.error('发送验证码失败:', error)
  }
}

// 重置密码
const handleResetPassword = async () => {
  if (!resetFormRef.value) return
  
  try {
    await resetFormRef.value.validate()
    resetLoading.value = true
    
    await authApi.resetPassword({
      username: resetForm.username,
      code: resetForm.code,
      newPassword: resetForm.newPassword,
      type: resetForm.type as 'sms' | 'email'
    })
    
    ElMessage.success('密码重置成功')
    showForgotPassword.value = false
  } catch (error) {
    console.error('重置密码失败:', error)
  } finally {
    resetLoading.value = false
  }
}

onMounted(() => {
  // 如果已经登录，直接跳转到首页
  if (userStore.isLoggedIn) {
    router.push('/dashboard')
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .logo {
    margin-bottom: 16px;
  }
  
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  .subtitle {
    color: #909399;
    font-size: 14px;
  }
}

.login-form {
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}

.captcha-container {
  display: flex;
  gap: 12px;
  
  .el-input {
    flex: 1;
  }
  
  .captcha-image {
    width: 100px;
    height: 40px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: #f5f7fa;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    span {
      font-size: 12px;
      color: #909399;
    }
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.third-party-login {
  margin-top: 24px;
  
  .divider-text {
    color: #909399;
    font-size: 12px;
  }
  
  .third-party-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 16px;
  }
}

.code-input-container {
  display: flex;
  gap: 12px;
  
  .el-input {
    flex: 1;
  }
  
  .el-button {
    white-space: nowrap;
  }
}

@media (max-width: 480px) {
  .login-box {
    padding: 24px;
  }
  
  .login-header .title {
    font-size: 20px;
  }
}
</style>
