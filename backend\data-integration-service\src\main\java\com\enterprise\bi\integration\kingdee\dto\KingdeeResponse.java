package com.enterprise.bi.integration.kingdee.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 金蝶API响应对象
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
public class KingdeeResponse {
    
    /**
     * 响应结果
     */
    @JsonProperty("Result")
    private Result result;
    
    /**
     * 响应ID
     */
    @JsonProperty("ResponseStatus")
    private ResponseStatus responseStatus;
    
    /**
     * 判断响应是否成功
     */
    public boolean isSuccess() {
        return responseStatus != null && 
               responseStatus.getIsSuccess() != null && 
               responseStatus.getIsSuccess();
    }
    
    /**
     * 获取错误消息
     */
    public String getMessage() {
        if (responseStatus != null && responseStatus.getErrors() != null && !responseStatus.getErrors().isEmpty()) {
            return responseStatus.getErrors().get(0).getMessage();
        }
        return "未知错误";
    }
    
    /**
     * 获取响应数据
     */
    public Object getData() {
        return result != null ? result.getResponseStatus() : null;
    }
    
    @Data
    public static class Result {
        @JsonProperty("ResponseStatus")
        private Object responseStatus;
        
        @JsonProperty("Result")
        private Object result;
    }
    
    @Data
    public static class ResponseStatus {
        @JsonProperty("IsSuccess")
        private Boolean isSuccess;
        
        @JsonProperty("Errors")
        private List<ErrorInfo> errors;
        
        @JsonProperty("SuccessEntitys")
        private List<Object> successEntitys;
        
        @JsonProperty("SuccessMessages")
        private List<Object> successMessages;
    }
    
    @Data
    public static class ErrorInfo {
        @JsonProperty("FieldName")
        private String fieldName;
        
        @JsonProperty("Message")
        private String message;
        
        @JsonProperty("DIndex")
        private Integer dIndex;
    }
}
