package com.enterprise.bi.visualization.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 仪表板组件实体类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "dashboard_widgets")
@EqualsAndHashCode(callSuper = false)
public class DashboardWidget {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 仪表板ID
     */
    @Column(name = "dashboard_id", nullable = false)
    private Long dashboardId;
    
    /**
     * 图表ID
     */
    @Column(name = "chart_id")
    private Long chartId;
    
    /**
     * 组件类型：CHART, TEXT, IMAGE, IFRAME, METRIC
     */
    @Column(name = "widget_type", nullable = false, length = 50)
    private String widgetType;
    
    /**
     * 组件标题
     */
    @Column(name = "title", length = 200)
    private String title;
    
    /**
     * 组件位置X
     */
    @Column(name = "position_x", nullable = false)
    private Integer positionX = 0;
    
    /**
     * 组件位置Y
     */
    @Column(name = "position_y", nullable = false)
    private Integer positionY = 0;
    
    /**
     * 组件宽度
     */
    @Column(name = "width", nullable = false)
    private Integer width = 4;
    
    /**
     * 组件高度
     */
    @Column(name = "height", nullable = false)
    private Integer height = 3;
    
    /**
     * 组件配置（JSON格式）
     */
    @Column(name = "widget_config", columnDefinition = "TEXT")
    private String widgetConfig;
    
    /**
     * 样式配置（JSON格式）
     */
    @Column(name = "style_config", columnDefinition = "TEXT")
    private String styleConfig;
    
    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * 仪表板关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dashboard_id", insertable = false, updatable = false)
    private Dashboard dashboard;
    
    /**
     * 图表关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chart_id", insertable = false, updatable = false)
    private Chart chart;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
