package com.enterprise.bi.integration.kingdee.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 同步记录实体
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "sync_records")
@EqualsAndHashCode(callSuper = false)
public class SyncRecord {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的同步任务ID
     */
    @Column(name = "task_id", nullable = false)
    private Long taskId;
    
    /**
     * 任务名称
     */
    @Column(name = "task_name", nullable = false, length = 100)
    private String taskName;
    
    /**
     * 数据类型
     */
    @Column(name = "data_type", nullable = false, length = 50)
    private String dataType;
    
    /**
     * 同步状态：RUNNING, SUCCESS, FAILED
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status;
    
    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private LocalDateTime endTime;
    
    /**
     * 同步数量
     */
    @Column(name = "sync_count")
    private Integer syncCount = 0;
    
    /**
     * 错误数量
     */
    @Column(name = "error_count")
    private Integer errorCount = 0;
    
    /**
     * 消息
     */
    @Column(name = "message", columnDefinition = "TEXT")
    private String message;
    
    /**
     * 错误详情
     */
    @Column(name = "error_detail", columnDefinition = "TEXT")
    private String errorDetail;
    
    /**
     * 同步配置快照
     */
    @Column(name = "sync_config_snapshot", columnDefinition = "TEXT")
    private String syncConfigSnapshot;
    
    /**
     * 执行时长（毫秒）
     */
    @Column(name = "duration_ms")
    private Long durationMs;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
    }
    
    /**
     * 计算执行时长
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }
}
