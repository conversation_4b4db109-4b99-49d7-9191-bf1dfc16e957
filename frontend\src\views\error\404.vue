<template>
  <div class="error-container">
    <div class="error-content">
      <div class="error-image">
        <el-icon size="120" color="#C0C4CC">
          <DocumentDelete />
        </el-icon>
      </div>
      <h1 class="error-title">404</h1>
      <p class="error-message">抱歉，您访问的页面不存在</p>
      <p class="error-description">
        可能是页面地址错误，或者页面已被删除
      </p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.error-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  
  .error-image {
    margin-bottom: 32px;
  }
  
  .error-title {
    font-size: 72px;
    font-weight: 700;
    color: #409EFF;
    margin-bottom: 16px;
    line-height: 1;
  }
  
  .error-message {
    font-size: 24px;
    color: #303133;
    margin-bottom: 12px;
  }
  
  .error-description {
    font-size: 16px;
    color: #909399;
    margin-bottom: 32px;
    line-height: 1.5;
  }
  
  .error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .error-content {
    padding: 20px;
    
    .error-title {
      font-size: 48px;
    }
    
    .error-message {
      font-size: 20px;
    }
    
    .error-description {
      font-size: 14px;
    }
    
    .error-actions {
      flex-direction: column;
    }
  }
}
</style>
