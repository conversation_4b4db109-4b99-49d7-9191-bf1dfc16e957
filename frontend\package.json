{"name": "enterprise-bi-platform-frontend", "version": "1.0.0", "description": "企业数据可视化BI平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test:unit": "vitest", "test:e2e": "playwright test"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "@vueuse/core": "^10.7.0", "nprogress": "^0.2.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "prettier": "^3.1.1", "typescript": "~5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.10", "vitest": "^1.1.0", "vue-tsc": "^1.8.25", "@playwright/test": "^1.40.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}