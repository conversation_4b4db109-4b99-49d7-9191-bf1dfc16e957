package com.enterprise.bi.visualization;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 可视化服务启动类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableKafka
@EnableAsync
@EnableScheduling
public class VisualizationServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(VisualizationServiceApplication.class, args);
    }
}
