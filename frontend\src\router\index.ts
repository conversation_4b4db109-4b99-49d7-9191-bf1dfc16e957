import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/Index.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true
    }
  },
  {
    path: '/data-integration',
    name: 'DataIntegration',
    component: () => import('@/views/data-integration/Index.vue'),
    meta: {
      title: '数据集成',
      requiresAuth: true
    }
  },
  {
    path: '/visualization',
    name: 'Visualization',
    component: () => import('@/views/visualization/Index.vue'),
    meta: {
      title: '数据可视化',
      requiresAuth: true
    }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: () => import('@/views/reports/Index.vue'),
    meta: {
      title: '报表管理',
      requiresAuth: true
    }
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/views/system/Index.vue'),
    meta: {
      title: '系统管理',
      requiresAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 企业数据可视化BI平台`
  }
  
  // 检查是否需要认证
  if (to.meta?.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查权限
    if (to.meta?.permissions && !userStore.hasPermissions(to.meta.permissions as string[])) {
      // 无权限，跳转到403页面
      next('/403')
      return
    }
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
