package com.enterprise.bi.processing.processor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据清洗处理器
 * 负责数据去重、空值处理、格式标准化等清洗操作
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class DataCleaningProcessor implements DataProcessor {
    
    @Override
    public Object process(Object data, Map<String, Object> config) {
        log.debug("开始执行数据清洗处理");
        
        if (!validateInput(data)) {
            throw new IllegalArgumentException("输入数据格式不正确");
        }
        
        if (data instanceof List) {
            return processListData((List<?>) data, config);
        } else if (data instanceof Map) {
            return processMapData((Map<?, ?>) data, config);
        }
        
        return data;
    }
    
    /**
     * 处理列表数据
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> processListData(List<?> dataList, Map<String, Object> config) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Object item : dataList) {
            if (item instanceof Map) {
                Map<String, Object> cleanedItem = cleanMapData((Map<String, Object>) item, config);
                if (cleanedItem != null && !cleanedItem.isEmpty()) {
                    result.add(cleanedItem);
                }
            }
        }
        
        // 去重处理
        if (getBooleanConfig(config, "removeDuplicates", true)) {
            result = removeDuplicates(result, config);
        }
        
        log.debug("数据清洗完成，原始数据量: {}, 清洗后数据量: {}", dataList.size(), result.size());
        return result;
    }
    
    /**
     * 处理Map数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> processMapData(Map<?, ?> dataMap, Map<String, Object> config) {
        return cleanMapData((Map<String, Object>) dataMap, config);
    }
    
    /**
     * 清洗Map数据
     */
    private Map<String, Object> cleanMapData(Map<String, Object> dataMap, Map<String, Object> config) {
        Map<String, Object> cleanedMap = new HashMap<>();
        
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // 处理空值
            if (value == null) {
                if (getBooleanConfig(config, "keepNullValues", false)) {
                    cleanedMap.put(key, null);
                }
                continue;
            }
            
            // 处理字符串
            if (value instanceof String) {
                String stringValue = (String) value;
                
                // 去除前后空格
                if (getBooleanConfig(config, "trimStrings", true)) {
                    stringValue = stringValue.trim();
                }
                
                // 处理空字符串
                if (StringUtils.isEmpty(stringValue)) {
                    if (getBooleanConfig(config, "keepEmptyStrings", false)) {
                        cleanedMap.put(key, stringValue);
                    }
                    continue;
                }
                
                // 标准化字符串格式
                stringValue = standardizeString(stringValue, config);
                cleanedMap.put(key, stringValue);
                
            } else if (value instanceof Number) {
                // 处理数值
                Number numberValue = processNumber((Number) value, config);
                if (numberValue != null) {
                    cleanedMap.put(key, numberValue);
                }
                
            } else {
                // 其他类型直接保留
                cleanedMap.put(key, value);
            }
        }
        
        return cleanedMap;
    }
    
    /**
     * 标准化字符串
     */
    private String standardizeString(String value, Map<String, Object> config) {
        // 转换为统一大小写
        String caseMode = getStringConfig(config, "caseMode", "none");
        switch (caseMode.toLowerCase()) {
            case "upper":
                value = value.toUpperCase();
                break;
            case "lower":
                value = value.toLowerCase();
                break;
            case "title":
                value = StringUtils.capitalize(value.toLowerCase());
                break;
        }
        
        // 移除特殊字符
        if (getBooleanConfig(config, "removeSpecialChars", false)) {
            value = value.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5\\s]", "");
        }
        
        // 标准化空格
        if (getBooleanConfig(config, "normalizeSpaces", true)) {
            value = value.replaceAll("\\s+", " ");
        }
        
        return value;
    }
    
    /**
     * 处理数值
     */
    private Number processNumber(Number value, Map<String, Object> config) {
        // 检查数值范围
        if (config.containsKey("minValue")) {
            double minValue = ((Number) config.get("minValue")).doubleValue();
            if (value.doubleValue() < minValue) {
                return getBooleanConfig(config, "replaceOutOfRange", false) ? minValue : null;
            }
        }
        
        if (config.containsKey("maxValue")) {
            double maxValue = ((Number) config.get("maxValue")).doubleValue();
            if (value.doubleValue() > maxValue) {
                return getBooleanConfig(config, "replaceOutOfRange", false) ? maxValue : null;
            }
        }
        
        return value;
    }
    
    /**
     * 去重处理
     */
    private List<Map<String, Object>> removeDuplicates(List<Map<String, Object>> dataList, Map<String, Object> config) {
        @SuppressWarnings("unchecked")
        List<String> uniqueKeys = (List<String>) config.get("uniqueKeys");
        
        if (uniqueKeys == null || uniqueKeys.isEmpty()) {
            // 基于所有字段去重
            return dataList.stream()
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            // 基于指定字段去重
            Set<String> seen = new HashSet<>();
            return dataList.stream()
                    .filter(item -> {
                        String key = uniqueKeys.stream()
                                .map(k -> String.valueOf(item.get(k)))
                                .collect(Collectors.joining("|"));
                        return seen.add(key);
                    })
                    .collect(Collectors.toList());
        }
    }
    
    /**
     * 获取布尔配置
     */
    private boolean getBooleanConfig(Map<String, Object> config, String key, boolean defaultValue) {
        Object value = config.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }
    
    /**
     * 获取字符串配置
     */
    private String getStringConfig(Map<String, Object> config, String key, String defaultValue) {
        Object value = config.get(key);
        if (value instanceof String) {
            return (String) value;
        }
        return defaultValue;
    }
    
    @Override
    public String getProcessorName() {
        return "DataCleaningProcessor";
    }
    
    @Override
    public String getProcessorDescription() {
        return "数据清洗处理器，负责数据去重、空值处理、格式标准化等操作";
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data instanceof List || data instanceof Map;
    }
    
    @Override
    public Class<?>[] getSupportedDataTypes() {
        return new Class<?>[]{List.class, Map.class};
    }
}
