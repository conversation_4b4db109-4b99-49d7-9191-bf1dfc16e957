package com.enterprise.bi.user.controller;

import com.enterprise.bi.user.dto.LoginRequest;
import com.enterprise.bi.user.dto.RefreshTokenRequest;
import com.enterprise.bi.user.dto.ApiResponse;
import com.enterprise.bi.user.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;

/**
 * 认证控制器
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AuthService authService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> login(@Valid @RequestBody LoginRequest loginRequest) {
        AuthService.LoginResult result = authService.login(
                loginRequest.getUsername(), 
                loginRequest.getPassword()
        );
        
        if (result.isSuccess()) {
            Map<String, Object> data = Map.of(
                "accessToken", result.getAccessToken(),
                "refreshToken", result.getRefreshToken(),
                "user", result.getUser()
            );
            return ResponseEntity.ok(ApiResponse.success("登录成功", data));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(result.getMessage()));
        }
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse<Map<String, Object>>> refreshToken(
            @Valid @RequestBody RefreshTokenRequest refreshRequest) {
        
        AuthService.RefreshResult result = authService.refreshToken(refreshRequest.getRefreshToken());
        
        if (result.isSuccess()) {
            Map<String, Object> data = Map.of("accessToken", result.getAccessToken());
            return ResponseEntity.ok(ApiResponse.success("令牌刷新成功", data));
        } else {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(result.getMessage()));
        }
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            String accessToken = null;
            
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                accessToken = authHeader.substring(7);
            }
            
            if (accessToken != null) {
                String username = authService.getUsernameFromToken(accessToken);
                authService.logout(username, accessToken, null);
            }
            
            return ResponseEntity.ok(ApiResponse.success("登出成功"));
        } catch (Exception e) {
            log.error("登出失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("登出失败"));
        }
    }
    
    /**
     * 验证令牌
     */
    @PostMapping("/validate")
    public ResponseEntity<ApiResponse<Map<String, Object>>> validateToken(HttpServletRequest request) {
        try {
            String authHeader = request.getHeader("Authorization");
            String token = null;
            
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            }
            
            if (token == null) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("令牌不能为空"));
            }
            
            boolean isValid = authService.validateToken(token);
            
            if (isValid) {
                String username = authService.getUsernameFromToken(token);
                Map<String, Object> data = Map.of(
                    "valid", true,
                    "username", username
                );
                return ResponseEntity.ok(ApiResponse.success("令牌有效", data));
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("令牌无效"));
            }
        } catch (Exception e) {
            log.error("验证令牌失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("验证令牌失败"));
        }
    }
}
