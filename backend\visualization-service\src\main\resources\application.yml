server:
  port: 8084
  servlet:
    context-path: /api/visualization

spring:
  application:
    name: visualization-service
  
  # 数据库配置
  datasource:
    url: **********************************************
    username: ${DB_USERNAME:bi_user}
    password: ${DB_PASSWORD:bi_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 3
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # Kafka配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
    consumer:
      group-id: visualization-service-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      properties:
        spring.json.trusted.packages: "*"

# ClickHouse配置
clickhouse:
  url: **********************************************
  username: ${CLICKHOUSE_USERNAME:default}
  password: ${CLICKHOUSE_PASSWORD:}
  driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
  connection-timeout: 30000
  socket-timeout: 60000
  max-connections: 10

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.enterprise.bi: DEBUG
    org.springframework.kafka: INFO
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/visualization-service.log
    max-size: 100MB
    max-history: 30

# 自定义配置
app:
  # 可视化配置
  visualization:
    # 图表配置
    chart:
      # 默认缓存时间（秒）
      default-cache-duration: 300
      # 默认刷新间隔（秒）
      default-refresh-interval: 300
      # 最大查询超时时间（秒）
      max-query-timeout: 60
      # 最大返回记录数
      max-result-size: 10000
      # 支持的图表类型
      supported-types:
        - LINE
        - BAR
        - PIE
        - SCATTER
        - AREA
        - GAUGE
        - FUNNEL
        - RADAR
        - HEATMAP
        - TREEMAP
    
    # 仪表板配置
    dashboard:
      # 默认网格大小
      grid-size: 24
      # 最大组件数量
      max-widgets: 50
      # 默认刷新间隔（秒）
      default-refresh-interval: 60
      # 支持的组件类型
      supported-widget-types:
        - CHART
        - TEXT
        - IMAGE
        - IFRAME
        - METRIC
        - TABLE
    
    # 实时数据配置
    realtime:
      # WebSocket连接超时（秒）
      websocket-timeout: 300
      # 心跳间隔（秒）
      heartbeat-interval: 30
      # 最大并发连接数
      max-connections: 1000
      # 数据推送间隔（毫秒）
      push-interval: 1000
  
  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:enterprise-bi-platform-secret-key-2024}
      expiration: 86400000 # 24小时
  
  # 缓存配置
  cache:
    # 图表数据缓存
    chart-data:
      enabled: true
      default-ttl: 300 # 5分钟
      max-size: 10000
    
    # 仪表板缓存
    dashboard:
      enabled: true
      default-ttl: 600 # 10分钟
      max-size: 1000
  
  # 性能配置
  performance:
    # 异步执行配置
    async:
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 1000
      keep-alive-seconds: 60
    
    # 查询优化
    query:
      # 启用查询缓存
      enable-cache: true
      # 启用查询优化
      enable-optimization: true
      # 慢查询阈值（毫秒）
      slow-query-threshold: 5000
