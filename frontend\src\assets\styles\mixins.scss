// SCSS混入(Mixins)

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 居中对齐
@mixin center($position: absolute) {
  position: $position;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 垂直居中
@mixin vertical-center($position: absolute) {
  position: $position;
  top: 50%;
  transform: translateY(-50%);
}

// 水平居中
@mixin horizontal-center($position: absolute) {
  position: $position;
  left: 50%;
  transform: translateX(-50%);
}

// Flex布局
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// 响应式断点
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-xs}) and (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
}

// 按钮样式
@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover {
    color: $color;
    background-color: lighten($background, 10%);
    border-color: lighten($border, 10%);
  }
  
  &:active {
    color: $color;
    background-color: darken($background, 10%);
    border-color: darken($border, 10%);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 卡片样式
@mixin card($padding: $spacing-lg, $radius: $border-radius, $shadow: $box-shadow) {
  background: $card-bg;
  border-radius: $radius;
  box-shadow: $shadow;
  padding: $padding;
}

// 输入框样式
@mixin input-variant($border-color: $border-color, $focus-color: $primary-color) {
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: $spacing-sm $spacing-md;
  transition: $transition-base;
  
  &:focus {
    border-color: $focus-color;
    box-shadow: 0 0 0 2px rgba($focus-color, 0.2);
    outline: none;
  }
  
  &:disabled {
    background-color: $bg-color;
    cursor: not-allowed;
  }
}

// 加载动画
@mixin loading-spinner($size: 20px, $color: $primary-color) {
  width: $size;
  height: $size;
  border: 2px solid rgba($color, 0.2);
  border-top: 2px solid $color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 渐变背景
@mixin gradient-background($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 阴影效果
@mixin box-shadow-variant($level: 1) {
  @if $level == 1 {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  } @else if $level == 2 {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 12px rgba(0, 0, 0, 0.08);
  } @else if $level == 3 {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12), 0 0 24px rgba(0, 0, 0, 0.12);
  }
}

// 过渡动画
@mixin transition($property: all, $duration: 0.3s, $timing: ease) {
  transition: $property $duration $timing;
}

// 文字渐变
@mixin text-gradient($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 毛玻璃效果
@mixin backdrop-blur($blur: 10px, $opacity: 0.8) {
  backdrop-filter: blur($blur);
  background-color: rgba(255, 255, 255, $opacity);
}

// 滚动条样式
@mixin scrollbar($width: 8px, $track-color: $scrollbar-track-color, $thumb-color: $scrollbar-thumb-color) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}
