package com.enterprise.bi.processing.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据聚合处理器
 * 负责数据分组、统计、聚合计算等操作
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class DataAggregationProcessor implements DataProcessor {
    
    @Override
    public Object process(Object data, Map<String, Object> config) {
        log.debug("开始执行数据聚合处理");
        
        if (!validateInput(data)) {
            throw new IllegalArgumentException("输入数据格式不正确");
        }
        
        if (data instanceof List) {
            return processListData((List<?>) data, config);
        }
        
        return data;
    }
    
    /**
     * 处理列表数据
     */
    @SuppressWarnings("unchecked")
    private Object processListData(List<?> dataList, Map<String, Object> config) {
        List<Map<String, Object>> mapList = dataList.stream()
                .filter(item -> item instanceof Map)
                .map(item -> (Map<String, Object>) item)
                .collect(Collectors.toList());
        
        String aggregationType = getStringConfig(config, "aggregationType", "GROUP_BY");
        
        switch (aggregationType.toUpperCase()) {
            case "GROUP_BY":
                return performGroupBy(mapList, config);
            case "SUMMARY":
                return performSummary(mapList, config);
            case "PIVOT":
                return performPivot(mapList, config);
            default:
                log.warn("不支持的聚合类型: {}", aggregationType);
                return dataList;
        }
    }
    
    /**
     * 执行分组聚合
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> performGroupBy(List<Map<String, Object>> dataList, Map<String, Object> config) {
        List<String> groupByFields = (List<String>) config.get("groupByFields");
        Map<String, String> aggregationFunctions = (Map<String, String>) config.get("aggregationFunctions");
        
        if (groupByFields == null || groupByFields.isEmpty()) {
            log.warn("分组字段为空，返回原始数据");
            return dataList;
        }
        
        // 按分组字段分组
        Map<String, List<Map<String, Object>>> groupedData = dataList.stream()
                .collect(Collectors.groupingBy(item -> 
                    groupByFields.stream()
                            .map(field -> String.valueOf(item.get(field)))
                            .collect(Collectors.joining("|"))
                ));
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
            String groupKey = entry.getKey();
            List<Map<String, Object>> groupItems = entry.getValue();
            
            Map<String, Object> aggregatedItem = new HashMap<>();
            
            // 添加分组字段
            String[] groupValues = groupKey.split("\\|");
            for (int i = 0; i < groupByFields.size() && i < groupValues.length; i++) {
                aggregatedItem.put(groupByFields.get(i), groupValues[i]);
            }
            
            // 执行聚合函数
            if (aggregationFunctions != null) {
                for (Map.Entry<String, String> funcEntry : aggregationFunctions.entrySet()) {
                    String field = funcEntry.getKey();
                    String function = funcEntry.getValue();
                    Object aggregatedValue = applyAggregationFunction(groupItems, field, function);
                    aggregatedItem.put(field + "_" + function.toLowerCase(), aggregatedValue);
                }
            }
            
            // 添加记录数
            aggregatedItem.put("_count", groupItems.size());
            
            result.add(aggregatedItem);
        }
        
        log.debug("分组聚合完成，原始数据量: {}, 聚合后数据量: {}", dataList.size(), result.size());
        return result;
    }
    
    /**
     * 执行汇总统计
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> performSummary(List<Map<String, Object>> dataList, Map<String, Object> config) {
        Map<String, String> summaryFunctions = (Map<String, String>) config.get("summaryFunctions");
        Map<String, Object> summary = new HashMap<>();
        
        summary.put("totalRecords", dataList.size());
        
        if (summaryFunctions != null) {
            for (Map.Entry<String, String> entry : summaryFunctions.entrySet()) {
                String field = entry.getKey();
                String function = entry.getValue();
                Object summaryValue = applyAggregationFunction(dataList, field, function);
                summary.put(field + "_" + function.toLowerCase(), summaryValue);
            }
        }
        
        log.debug("汇总统计完成，统计字段数: {}", summary.size());
        return summary;
    }
    
    /**
     * 执行透视表操作
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> performPivot(List<Map<String, Object>> dataList, Map<String, Object> config) {
        String rowField = getStringConfig(config, "rowField", "");
        String columnField = getStringConfig(config, "columnField", "");
        String valueField = getStringConfig(config, "valueField", "");
        String aggregationFunction = getStringConfig(config, "aggregationFunction", "SUM");
        
        if (rowField.isEmpty() || columnField.isEmpty() || valueField.isEmpty()) {
            log.warn("透视表配置不完整");
            return Map.of("error", "透视表配置不完整");
        }
        
        Map<String, Map<String, List<Object>>> pivotData = new HashMap<>();
        
        // 构建透视数据结构
        for (Map<String, Object> item : dataList) {
            String rowValue = String.valueOf(item.get(rowField));
            String columnValue = String.valueOf(item.get(columnField));
            Object value = item.get(valueField);
            
            pivotData.computeIfAbsent(rowValue, k -> new HashMap<>())
                    .computeIfAbsent(columnValue, k -> new ArrayList<>())
                    .add(value);
        }
        
        // 应用聚合函数
        Map<String, Map<String, Object>> result = new HashMap<>();
        for (Map.Entry<String, Map<String, List<Object>>> rowEntry : pivotData.entrySet()) {
            String rowValue = rowEntry.getKey();
            Map<String, Object> columnData = new HashMap<>();
            
            for (Map.Entry<String, List<Object>> columnEntry : rowEntry.getValue().entrySet()) {
                String columnValue = columnEntry.getKey();
                List<Object> values = columnEntry.getValue();
                Object aggregatedValue = applyAggregationFunction(values, aggregationFunction);
                columnData.put(columnValue, aggregatedValue);
            }
            
            result.put(rowValue, columnData);
        }
        
        log.debug("透视表操作完成，行数: {}", result.size());
        return Map.of("pivotTable", result);
    }
    
    /**
     * 应用聚合函数
     */
    private Object applyAggregationFunction(List<Map<String, Object>> dataList, String field, String function) {
        List<Object> values = dataList.stream()
                .map(item -> item.get(field))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        return applyAggregationFunction(values, function);
    }
    
    /**
     * 应用聚合函数到值列表
     */
    private Object applyAggregationFunction(List<Object> values, String function) {
        if (values.isEmpty()) {
            return null;
        }
        
        switch (function.toUpperCase()) {
            case "COUNT":
                return values.size();
                
            case "SUM":
                return values.stream()
                        .filter(v -> v instanceof Number)
                        .mapToDouble(v -> ((Number) v).doubleValue())
                        .sum();
                        
            case "AVG":
                return values.stream()
                        .filter(v -> v instanceof Number)
                        .mapToDouble(v -> ((Number) v).doubleValue())
                        .average()
                        .orElse(0.0);
                        
            case "MIN":
                return values.stream()
                        .filter(v -> v instanceof Number)
                        .mapToDouble(v -> ((Number) v).doubleValue())
                        .min()
                        .orElse(0.0);
                        
            case "MAX":
                return values.stream()
                        .filter(v -> v instanceof Number)
                        .mapToDouble(v -> ((Number) v).doubleValue())
                        .max()
                        .orElse(0.0);
                        
            case "FIRST":
                return values.get(0);
                
            case "LAST":
                return values.get(values.size() - 1);
                
            case "DISTINCT_COUNT":
                return values.stream().distinct().count();
                
            default:
                log.warn("不支持的聚合函数: {}", function);
                return values.size();
        }
    }
    
    /**
     * 获取字符串配置
     */
    private String getStringConfig(Map<String, Object> config, String key, String defaultValue) {
        Object value = config.get(key);
        if (value instanceof String) {
            return (String) value;
        }
        return defaultValue;
    }
    
    @Override
    public String getProcessorName() {
        return "DataAggregationProcessor";
    }
    
    @Override
    public String getProcessorDescription() {
        return "数据聚合处理器，负责数据分组、统计、聚合计算等操作";
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data instanceof List;
    }
    
    @Override
    public Class<?>[] getSupportedDataTypes() {
        return new Class<?>[]{List.class};
    }
}
