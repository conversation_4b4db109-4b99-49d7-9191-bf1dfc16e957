package com.enterprise.bi.processing.processor;

import java.util.Map;

/**
 * 数据处理器接口
 * 定义数据处理的基本契约
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
public interface DataProcessor {
    
    /**
     * 处理数据
     * 
     * @param data 输入数据
     * @param config 处理配置
     * @return 处理后的数据
     */
    Object process(Object data, Map<String, Object> config);
    
    /**
     * 获取处理器名称
     */
    String getProcessorName();
    
    /**
     * 获取处理器描述
     */
    String getProcessorDescription();
    
    /**
     * 验证输入数据
     */
    boolean validateInput(Object data);
    
    /**
     * 获取支持的数据类型
     */
    Class<?>[] getSupportedDataTypes();
}
