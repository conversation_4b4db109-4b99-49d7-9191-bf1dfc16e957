<template>
  <div class="data-integration-container">
    <div class="page-header">
      <h1 class="page-title">数据集成</h1>
      <p class="page-description">管理企业数据源集成和同步配置</p>
    </div>

    <!-- 数据源状态概览 -->
    <div class="status-overview">
      <div class="status-card" v-for="source in dataSources" :key="source.id">
        <div class="status-header">
          <div class="source-info">
            <el-icon :size="24" :color="source.statusColor">
              <component :is="source.icon" />
            </el-icon>
            <div class="source-details">
              <h3>{{ source.name }}</h3>
              <p>{{ source.description }}</p>
            </div>
          </div>
          <el-tag :type="source.status === 'connected' ? 'success' : 'danger'" size="small">
            {{ source.status === 'connected' ? '已连接' : '断开连接' }}
          </el-tag>
        </div>
        
        <div class="status-metrics">
          <div class="metric">
            <span class="metric-label">最后同步</span>
            <span class="metric-value">{{ formatTime(source.lastSync) }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">同步记录</span>
            <span class="metric-value">{{ source.syncCount.toLocaleString() }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">错误次数</span>
            <span class="metric-value error">{{ source.errorCount }}</span>
          </div>
        </div>
        
        <div class="status-actions">
          <el-button size="small" @click="testConnection(source.id)">
            测试连接
          </el-button>
          <el-button size="small" type="primary" @click="syncNow(source.id)">
            立即同步
          </el-button>
          <el-button size="small" @click="configureSource(source.id)">
            配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 同步任务列表 -->
    <div class="sync-tasks-section">
      <div class="section-header">
        <h3>同步任务</h3>
        <el-button type="primary" @click="showCreateTask = true">
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
      </div>
      
      <el-table :data="syncTasks" style="width: 100%">
        <el-table-column prop="name" label="任务名称" />
        <el-table-column prop="source" label="数据源" />
        <el-table-column prop="schedule" label="调度规则" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastRun" label="最后执行">
          <template #default="{ row }">
            {{ formatTime(row.lastRun) }}
          </template>
        </el-table-column>
        <el-table-column prop="nextRun" label="下次执行">
          <template #default="{ row }">
            {{ formatTime(row.nextRun) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="runTask(row.id)">
              执行
            </el-button>
            <el-button size="small" @click="editTask(row.id)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteTask(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateTask"
      title="创建同步任务"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="100px">
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
        </el-form-item>
        <el-form-item label="数据源" prop="sourceId">
          <el-select v-model="taskForm.sourceId" placeholder="请选择数据源" style="width: 100%">
            <el-option
              v-for="source in dataSources"
              :key="source.id"
              :label="source.name"
              :value="source.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="同步类型" prop="syncType">
          <el-radio-group v-model="taskForm.syncType">
            <el-radio label="full">全量同步</el-radio>
            <el-radio label="incremental">增量同步</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调度规则" prop="schedule">
          <el-select v-model="taskForm.schedule" placeholder="请选择调度规则" style="width: 100%">
            <el-option label="每5分钟" value="0 */5 * * * ?" />
            <el-option label="每小时" value="0 0 * * * ?" />
            <el-option label="每天凌晨2点" value="0 0 2 * * ?" />
            <el-option label="每周一凌晨2点" value="0 0 2 ? * MON" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="taskForm.schedule === 'custom'" label="Cron表达式" prop="cronExpression">
          <el-input v-model="taskForm.cronExpression" placeholder="请输入Cron表达式" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateTask = false">取消</el-button>
        <el-button type="primary" @click="createTask">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { formatTime } from '@/utils/date'

// 表单引用
const taskFormRef = ref<FormInstance>()

// 状态
const showCreateTask = ref(false)

// 数据源状态
const dataSources = ref([
  {
    id: 'kingdee',
    name: '金蝶云星辰',
    description: 'ERP系统数据集成',
    icon: 'DataBoard',
    status: 'connected',
    statusColor: '#67C23A',
    lastSync: new Date(Date.now() - 5 * 60 * 1000),
    syncCount: 12456,
    errorCount: 2
  },
  {
    id: 'dingtalk',
    name: '钉钉',
    description: '组织架构和消息推送',
    icon: 'ChatDotRound',
    status: 'connected',
    statusColor: '#67C23A',
    lastSync: new Date(Date.now() - 10 * 60 * 1000),
    syncCount: 3456,
    errorCount: 0
  },
  {
    id: 'wechat',
    name: '企业微信',
    description: '企业通讯和协作',
    icon: 'ChatRound',
    status: 'disconnected',
    statusColor: '#F56C6C',
    lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
    syncCount: 1234,
    errorCount: 5
  }
])

// 同步任务
const syncTasks = ref([
  {
    id: 1,
    name: '金蝶订单同步',
    source: '金蝶云星辰',
    schedule: '每5分钟',
    status: 'running',
    lastRun: new Date(Date.now() - 3 * 60 * 1000),
    nextRun: new Date(Date.now() + 2 * 60 * 1000)
  },
  {
    id: 2,
    name: '钉钉组织架构同步',
    source: '钉钉',
    schedule: '每天凌晨2点',
    status: 'success',
    lastRun: new Date(Date.now() - 8 * 60 * 60 * 1000),
    nextRun: new Date(Date.now() + 16 * 60 * 60 * 1000)
  },
  {
    id: 3,
    name: '企业微信消息同步',
    source: '企业微信',
    schedule: '每小时',
    status: 'failed',
    lastRun: new Date(Date.now() - 1 * 60 * 60 * 1000),
    nextRun: new Date(Date.now() + 1 * 60 * 60 * 1000)
  }
])

// 任务表单
const taskForm = reactive({
  name: '',
  sourceId: '',
  syncType: 'incremental',
  schedule: '',
  cronExpression: '',
  description: ''
})

// 表单验证规则
const taskRules: FormRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  sourceId: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  schedule: [
    { required: true, message: '请选择调度规则', trigger: 'change' }
  ]
}

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'primary',
    success: 'success',
    failed: 'danger',
    stopped: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '运行中',
    success: '成功',
    failed: '失败',
    stopped: '已停止'
  }
  return statusMap[status] || '未知'
}

// 测试连接
const testConnection = async (sourceId: string) => {
  try {
    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败')
  }
}

// 立即同步
const syncNow = async (sourceId: string) => {
  try {
    ElMessage.success('同步任务已启动')
  } catch (error) {
    ElMessage.error('同步任务启动失败')
  }
}

// 配置数据源
const configureSource = (sourceId: string) => {
  ElMessage.info('配置功能开发中...')
}

// 执行任务
const runTask = async (taskId: number) => {
  try {
    ElMessage.success('任务执行成功')
  } catch (error) {
    ElMessage.error('任务执行失败')
  }
}

// 编辑任务
const editTask = (taskId: number) => {
  ElMessage.info('编辑功能开发中...')
}

// 删除任务
const deleteTask = async (taskId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('任务删除成功')
  } catch (error) {
    // 用户取消
  }
}

// 创建任务
const createTask = async () => {
  if (!taskFormRef.value) return
  
  try {
    await taskFormRef.value.validate()
    ElMessage.success('任务创建成功')
    showCreateTask.value = false
    
    // 重置表单
    Object.assign(taskForm, {
      name: '',
      sourceId: '',
      syncType: 'incremental',
      schedule: '',
      cronExpression: '',
      description: ''
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.data-integration-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 24px;
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  .page-description {
    color: #909399;
    font-size: 14px;
  }
}

.status-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .source-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .source-details {
        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 4px 0;
        }
        
        p {
          font-size: 13px;
          color: #909399;
          margin: 0;
        }
      }
    }
  }
  
  .status-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 20px;
    
    .metric {
      text-align: center;
      
      .metric-label {
        display: block;
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }
      
      .metric-value {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        
        &.error {
          color: #f56c6c;
        }
      }
    }
  }
  
  .status-actions {
    display: flex;
    gap: 8px;
  }
}

.sync-tasks-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
  }
}

@media (max-width: 768px) {
  .data-integration-container {
    padding: 16px;
  }
  
  .status-overview {
    grid-template-columns: 1fr;
  }
  
  .status-metrics {
    grid-template-columns: 1fr;
  }
  
  .status-actions {
    flex-direction: column;
  }
}
</style>
