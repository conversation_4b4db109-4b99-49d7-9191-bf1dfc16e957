package com.enterprise.bi.visualization.service;

import com.enterprise.bi.visualization.entity.Dashboard;
import com.enterprise.bi.visualization.entity.DashboardWidget;
import com.enterprise.bi.visualization.repository.DashboardRepository;
import com.enterprise.bi.visualization.repository.DashboardWidgetRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 仪表板服务类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardService {
    
    private final DashboardRepository dashboardRepository;
    private final DashboardWidgetRepository widgetRepository;
    private final ChartService chartService;
    
    /**
     * 创建仪表板
     */
    @Transactional
    public Dashboard createDashboard(Dashboard dashboard) {
        Dashboard savedDashboard = dashboardRepository.save(dashboard);
        log.info("创建仪表板成功: {}", savedDashboard.getName());
        return savedDashboard;
    }
    
    /**
     * 更新仪表板
     */
    @Transactional
    public Dashboard updateDashboard(Long dashboardId, Dashboard dashboardUpdate) {
        Dashboard existingDashboard = dashboardRepository.findById(dashboardId)
                .orElseThrow(() -> new IllegalArgumentException("仪表板不存在: " + dashboardId));
        
        // 更新基本信息
        if (dashboardUpdate.getName() != null) {
            existingDashboard.setName(dashboardUpdate.getName());
        }
        if (dashboardUpdate.getDescription() != null) {
            existingDashboard.setDescription(dashboardUpdate.getDescription());
        }
        if (dashboardUpdate.getType() != null) {
            existingDashboard.setType(dashboardUpdate.getType());
        }
        if (dashboardUpdate.getLayoutConfig() != null) {
            existingDashboard.setLayoutConfig(dashboardUpdate.getLayoutConfig());
        }
        if (dashboardUpdate.getStyleConfig() != null) {
            existingDashboard.setStyleConfig(dashboardUpdate.getStyleConfig());
        }
        if (dashboardUpdate.getIsPublic() != null) {
            existingDashboard.setIsPublic(dashboardUpdate.getIsPublic());
        }
        if (dashboardUpdate.getSortOrder() != null) {
            existingDashboard.setSortOrder(dashboardUpdate.getSortOrder());
        }
        
        Dashboard savedDashboard = dashboardRepository.save(existingDashboard);
        log.info("更新仪表板成功: {}", savedDashboard.getName());
        
        return savedDashboard;
    }
    
    /**
     * 删除仪表板
     */
    @Transactional
    public void deleteDashboard(Long dashboardId) {
        Dashboard dashboard = dashboardRepository.findById(dashboardId)
                .orElseThrow(() -> new IllegalArgumentException("仪表板不存在: " + dashboardId));
        
        // 删除关联的组件
        widgetRepository.deleteByDashboardId(dashboardId);
        
        dashboardRepository.delete(dashboard);
        log.info("删除仪表板: {}", dashboard.getName());
    }
    
    /**
     * 根据ID获取仪表板
     */
    public Optional<Dashboard> getDashboardById(Long dashboardId) {
        return dashboardRepository.findById(dashboardId);
    }
    
    /**
     * 获取仪表板详情（包含组件）
     */
    public DashboardDetail getDashboardDetail(Long dashboardId) {
        Dashboard dashboard = dashboardRepository.findById(dashboardId)
                .orElseThrow(() -> new IllegalArgumentException("仪表板不存在: " + dashboardId));
        
        List<DashboardWidget> widgets = widgetRepository.findByDashboardIdOrderBySortOrder(dashboardId);
        
        // 更新访问统计
        updateViewStats(dashboard);
        
        return new DashboardDetail(dashboard, widgets);
    }
    
    /**
     * 分页获取仪表板列表
     */
    public Page<Dashboard> getDashboards(Pageable pageable) {
        return dashboardRepository.findAll(pageable);
    }
    
    /**
     * 根据类型获取仪表板列表
     */
    public List<Dashboard> getDashboardsByType(String type) {
        return dashboardRepository.findByType(type);
    }
    
    /**
     * 根据创建者获取仪表板列表
     */
    public List<Dashboard> getDashboardsByCreator(String createdBy) {
        return dashboardRepository.findByCreatedBy(createdBy);
    }
    
    /**
     * 获取公开的仪表板列表
     */
    public List<Dashboard> getPublicDashboards() {
        return dashboardRepository.findByIsPublicTrueAndIsEnabledTrueOrderBySortOrder();
    }
    
    /**
     * 搜索仪表板
     */
    public Page<Dashboard> searchDashboards(String keyword, Pageable pageable) {
        return dashboardRepository.findByNameContainingOrDescriptionContaining(
                keyword, keyword, pageable);
    }
    
    /**
     * 添加组件到仪表板
     */
    @Transactional
    public DashboardWidget addWidget(Long dashboardId, DashboardWidget widget) {
        // 验证仪表板存在
        dashboardRepository.findById(dashboardId)
                .orElseThrow(() -> new IllegalArgumentException("仪表板不存在: " + dashboardId));
        
        widget.setDashboardId(dashboardId);
        DashboardWidget savedWidget = widgetRepository.save(widget);
        
        log.info("添加组件到仪表板 {}: {}", dashboardId, widget.getTitle());
        
        return savedWidget;
    }
    
    /**
     * 更新组件
     */
    @Transactional
    public DashboardWidget updateWidget(Long widgetId, DashboardWidget widgetUpdate) {
        DashboardWidget existingWidget = widgetRepository.findById(widgetId)
                .orElseThrow(() -> new IllegalArgumentException("组件不存在: " + widgetId));
        
        // 更新组件信息
        if (widgetUpdate.getTitle() != null) {
            existingWidget.setTitle(widgetUpdate.getTitle());
        }
        if (widgetUpdate.getPositionX() != null) {
            existingWidget.setPositionX(widgetUpdate.getPositionX());
        }
        if (widgetUpdate.getPositionY() != null) {
            existingWidget.setPositionY(widgetUpdate.getPositionY());
        }
        if (widgetUpdate.getWidth() != null) {
            existingWidget.setWidth(widgetUpdate.getWidth());
        }
        if (widgetUpdate.getHeight() != null) {
            existingWidget.setHeight(widgetUpdate.getHeight());
        }
        if (widgetUpdate.getWidgetConfig() != null) {
            existingWidget.setWidgetConfig(widgetUpdate.getWidgetConfig());
        }
        if (widgetUpdate.getStyleConfig() != null) {
            existingWidget.setStyleConfig(widgetUpdate.getStyleConfig());
        }
        if (widgetUpdate.getSortOrder() != null) {
            existingWidget.setSortOrder(widgetUpdate.getSortOrder());
        }
        
        DashboardWidget savedWidget = widgetRepository.save(existingWidget);
        log.info("更新组件: {}", savedWidget.getTitle());
        
        return savedWidget;
    }
    
    /**
     * 删除组件
     */
    @Transactional
    public void deleteWidget(Long widgetId) {
        DashboardWidget widget = widgetRepository.findById(widgetId)
                .orElseThrow(() -> new IllegalArgumentException("组件不存在: " + widgetId));
        
        widgetRepository.delete(widget);
        log.info("删除组件: {}", widget.getTitle());
    }
    
    /**
     * 获取仪表板的所有数据
     */
    public DashboardDataResult getDashboardData(Long dashboardId) {
        Dashboard dashboard = dashboardRepository.findById(dashboardId)
                .orElseThrow(() -> new IllegalArgumentException("仪表板不存在: " + dashboardId));
        
        List<DashboardWidget> widgets = widgetRepository.findByDashboardIdOrderBySortOrder(dashboardId);
        
        // 获取所有图表数据
        List<Long> chartIds = widgets.stream()
                .filter(w -> "CHART".equals(w.getWidgetType()) && w.getChartId() != null)
                .map(DashboardWidget::getChartId)
                .toList();
        
        Map<Long, ChartService.ChartDataResult> chartData = chartService.executeCharts(chartIds);
        
        // 更新访问统计
        updateViewStats(dashboard);
        
        return new DashboardDataResult(dashboard, widgets, chartData);
    }
    
    /**
     * 启用/禁用仪表板
     */
    @Transactional
    public void toggleDashboardStatus(Long dashboardId, boolean enabled) {
        Dashboard dashboard = dashboardRepository.findById(dashboardId)
                .orElseThrow(() -> new IllegalArgumentException("仪表板不存在: " + dashboardId));
        
        dashboard.setIsEnabled(enabled);
        dashboardRepository.save(dashboard);
        
        log.info("仪表板 {} 状态更新为: {}", dashboard.getName(), enabled ? "启用" : "禁用");
    }
    
    /**
     * 更新访问统计
     */
    @Transactional
    private void updateViewStats(Dashboard dashboard) {
        dashboard.setViewCount(dashboard.getViewCount() + 1);
        dashboard.setLastViewedAt(LocalDateTime.now());
        dashboardRepository.save(dashboard);
    }
    
    /**
     * 获取仪表板统计信息
     */
    public DashboardStatistics getDashboardStatistics() {
        long totalDashboards = dashboardRepository.count();
        long enabledDashboards = dashboardRepository.countByIsEnabled(true);
        long publicDashboards = dashboardRepository.countByIsPublic(true);
        
        return new DashboardStatistics(totalDashboards, enabledDashboards, publicDashboards);
    }
    
    /**
     * 仪表板详情类
     */
    public static class DashboardDetail {
        private final Dashboard dashboard;
        private final List<DashboardWidget> widgets;
        
        public DashboardDetail(Dashboard dashboard, List<DashboardWidget> widgets) {
            this.dashboard = dashboard;
            this.widgets = widgets;
        }
        
        // Getters
        public Dashboard getDashboard() { return dashboard; }
        public List<DashboardWidget> getWidgets() { return widgets; }
    }
    
    /**
     * 仪表板数据结果类
     */
    public static class DashboardDataResult {
        private final Dashboard dashboard;
        private final List<DashboardWidget> widgets;
        private final Map<Long, ChartService.ChartDataResult> chartData;
        
        public DashboardDataResult(Dashboard dashboard, List<DashboardWidget> widgets,
                                  Map<Long, ChartService.ChartDataResult> chartData) {
            this.dashboard = dashboard;
            this.widgets = widgets;
            this.chartData = chartData;
        }
        
        // Getters
        public Dashboard getDashboard() { return dashboard; }
        public List<DashboardWidget> getWidgets() { return widgets; }
        public Map<Long, ChartService.ChartDataResult> getChartData() { return chartData; }
    }
    
    /**
     * 仪表板统计信息类
     */
    public static class DashboardStatistics {
        private final long totalDashboards;
        private final long enabledDashboards;
        private final long publicDashboards;
        
        public DashboardStatistics(long totalDashboards, long enabledDashboards, long publicDashboards) {
            this.totalDashboards = totalDashboards;
            this.enabledDashboards = enabledDashboards;
            this.publicDashboards = publicDashboards;
        }
        
        // Getters
        public long getTotalDashboards() { return totalDashboards; }
        public long getEnabledDashboards() { return enabledDashboards; }
        public long getPublicDashboards() { return publicDashboards; }
    }
}
