package com.enterprise.bi.integration.kingdee.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 同步任务实体
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "sync_tasks")
@EqualsAndHashCode(callSuper = false)
public class SyncTask {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 任务名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    /**
     * 任务描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 数据类型
     */
    @Column(name = "data_type", nullable = false, length = 50)
    private String dataType;
    
    /**
     * 同步配置（JSON格式）
     */
    @Column(name = "sync_config", columnDefinition = "TEXT")
    private String syncConfig;
    
    /**
     * 任务状态：ACTIVE, INACTIVE, PAUSED
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status = "ACTIVE";
    
    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;
    
    /**
     * 同步间隔（秒）
     */
    @Column(name = "sync_interval")
    private Integer syncInterval;
    
    /**
     * 最后同步时间
     */
    @Column(name = "last_sync_time")
    private LocalDateTime lastSyncTime;
    
    /**
     * 下次同步时间
     */
    @Column(name = "next_sync_time")
    private LocalDateTime nextSyncTime;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
