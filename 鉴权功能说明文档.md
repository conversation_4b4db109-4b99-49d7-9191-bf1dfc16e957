# 金蝶云星辰API鉴权功能说明文档

## 概述

本文档详细说明了金蝶云星辰API系统的鉴权功能实现，包括API网关鉴权、Token管理、缓存机制等核心功能。

## 鉴权架构

### 1. 整体架构

```
客户端请求 → Spring Boot应用 → API网关鉴权 → 金蝶云星辰API
                ↓
            Token缓存管理
```

### 2. 核心组件

- **AuthService**: 鉴权服务核心类
- **KingdeeApiClient**: 金蝶API客户端工具类
- **ApiGatewayClient**: API网关客户端
- **TokenCacheService**: Token缓存管理服务

## 鉴权流程

### 1. API网关鉴权流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant App as Spring Boot应用
    participant Gateway as API网关
    participant KingdeeAPI as 金蝶API

    Client->>App: 发起业务请求
    App->>App: 检查Token缓存
    alt Token不存在或已过期
        App->>Gateway: 请求获取App Token
        Gateway->>KingdeeAPI: 鉴权请求
        KingdeeAPI-->>Gateway: 返回Token
        Gateway-->>App: 返回Token
        App->>App: 缓存Token
    end
    App->>KingdeeAPI: 使用Token调用业务API
    KingdeeAPI-->>App: 返回业务数据
    App-->>Client: 返回处理结果
```

### 2. Token获取方式

#### 方式一：API网关鉴权（推荐）
```java
// 通过API网关获取Token
String appToken = authService.getAppTokenWithGateway();
```

#### 方式二：直接鉴权（备用）
```java
// 直接调用金蝶鉴权API
String appToken = authService.getAppToken();
```

## 核心类详解

### 1. AuthService 鉴权服务

#### 主要功能
- Token获取和管理
- 缓存控制
- 鉴权状态监控

#### 核心方法

```java
/**
 * 通过API网关获取App Token（推荐方式）
 */
public String getAppTokenWithGateway()

/**
 * 直接获取App Token（备用方式）
 */
public String getAppToken()

/**
 * 获取授权信息
 */
public Map<String, Object> getAuthorizeInfo()

/**
 * 清除缓存的token
 */
public void clearToken()
```

### 2. KingdeeApiClient API客户端

#### 主要功能
- 统一的API调用接口
- 自动Token管理
- 业务单据查询

#### 核心方法

```java
/**
 * 执行业务单据查询
 */
public Map<String, Object> executeBillQuery(String formId, Map<String, Object> data)

/**
 * 通过API网关获取Token
 */
public String getAppTokenWithGateway()
```

### 3. TokenCacheService 缓存服务

#### 缓存策略
- **缓存时间**: 23小时（Token有效期24小时，提前1小时刷新）
- **缓存键格式**: `gateway_token_default`
- **自动过期**: 支持TTL自动清理

#### 核心方法

```java
/**
 * 存储Token到缓存
 */
public void putToken(String key, String token, long ttlMillis)

/**
 * 从缓存获取Token
 */
public String getToken(String key)

/**
 * 检查Token是否存在
 */
public boolean hasToken(String key)
```

## 配置说明

### 1. 应用配置 (application.yml)

```yaml
# 金蝶云星辰API配置
kingdee:
  api:
    client-id: "305494"
    client-secret: "your-client-secret"
    third-party-instance-id: "447582935277768704"
    app-key: "OwLCyaiM"
    
# API网关配置
api:
  gateway:
    host: "https://api.kingdee.com"
    timeout: 30000
```

### 2. 环境变量

```bash
# 客户端密钥（生产环境必须使用环境变量）
KINGDEE_CLIENT_SECRET=your-actual-client-secret
```

## API端点说明

### 1. 鉴权相关端点

#### 获取授权信息
```http
GET /api/auth/auth-info
```

**响应示例**:
```json
{
  "app_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "method": "API Gateway",
  "cache_status": "hit",
  "expires_in": 82800
}
```

### 2. 业务API端点

#### 客户列表
```http
GET /api/customer/list
```

#### 商品列表
```http
GET /api/material/list
```

## 错误处理

### 1. 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 5001 | API不存在[网关] | 检查API路径和权限配置 |
| 401 | 未授权 | 检查客户端凭证配置 |
| 403 | 禁止访问 | 检查API权限和账号状态 |
| 500 | 服务器内部错误 | 查看应用日志排查问题 |

### 2. 错误处理策略

```java
try {
    String token = authService.getAppTokenWithGateway();
    // 业务逻辑
} catch (Exception e) {
    log.error("鉴权失败", e);
    // 降级处理或重试
}
```

## 安全最佳实践

### 1. 密钥管理
- ✅ 使用环境变量存储敏感信息
- ✅ 生产环境禁止硬编码密钥
- ✅ 定期轮换客户端密钥

### 2. Token安全
- ✅ Token仅在内存中缓存
- ✅ 应用重启自动清理缓存
- ✅ 支持手动清理Token缓存

### 3. 网络安全
- ✅ 使用HTTPS通信
- ✅ 配置合理的超时时间
- ✅ 实现重试机制

## 监控和日志

### 1. 关键日志

```java
// Token获取成功
log.info("通过API网关获取App Token成功");

// Token缓存命中
log.debug("从缓存获取到有效的App Token");

// API调用失败
log.error("金蝶云星辰API调用失败", exception);
```

### 2. 监控指标
- Token获取成功率
- API调用响应时间
- 缓存命中率
- 错误率统计

## 故障排查

### 1. Token获取失败
1. 检查客户端ID和密钥配置
2. 验证网络连接
3. 查看API网关状态
4. 检查账号权限

### 2. API调用失败
1. 确认Token有效性
2. 检查API路径和参数
3. 验证业务权限
4. 查看详细错误信息

### 3. 缓存问题
1. 检查缓存服务状态
2. 验证缓存配置
3. 手动清理缓存重试

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2025-09-19 | 初始版本，实现API网关鉴权 |

## 联系支持

如有问题，请联系技术支持团队或查看金蝶云星辰官方文档。