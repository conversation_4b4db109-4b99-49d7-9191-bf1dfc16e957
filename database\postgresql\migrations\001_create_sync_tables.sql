-- 创建企业BI平台数据库表结构
-- 版本: 1.0.0
-- 作者: Enterprise BI Team

-- 创建数据库（如果不存在）
-- CREATE DATABASE enterprise_bi;

-- 使用数据库
-- \c enterprise_bi;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    department VARCHAR(100),
    position VARCHAR(100),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    role VARCHAR(50) DEFAULT 'USER',
    avatar_url VARCHAR(500),
    last_login_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(50),
    updated_by <PERSON><PERSON><PERSON><PERSON>(50)
);

-- 创建同步任务表
CREATE TABLE IF NOT EXISTS sync_tasks (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    data_type VARCHAR(50) NOT NULL,
    sync_config TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    enabled BOOLEAN NOT NULL DEFAULT true,
    sync_interval INTEGER,
    last_sync_time TIMESTAMP,
    next_sync_time TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50)
);

-- 创建同步记录表
CREATE TABLE IF NOT EXISTS sync_records (
    id BIGSERIAL PRIMARY KEY,
    task_id BIGINT NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    sync_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    message TEXT,
    error_detail TEXT,
    sync_config_snapshot TEXT,
    duration_ms BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES sync_tasks(id) ON DELETE CASCADE
);

-- 创建数据源配置表
CREATE TABLE IF NOT EXISTS data_sources (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    connection_config TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50)
);

-- 创建数据映射表
CREATE TABLE IF NOT EXISTS data_mappings (
    id BIGSERIAL PRIMARY KEY,
    source_table VARCHAR(100) NOT NULL,
    target_table VARCHAR(100) NOT NULL,
    field_mappings TEXT NOT NULL,
    transformation_rules TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50)
);

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(50) DEFAULT 'STRING',
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50)
);

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    username VARCHAR(50),
    operation VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(100),
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    status VARCHAR(20) DEFAULT 'SUCCESS',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sync_tasks_status ON sync_tasks(status);
CREATE INDEX IF NOT EXISTS idx_sync_tasks_data_type ON sync_tasks(data_type);
CREATE INDEX IF NOT EXISTS idx_sync_tasks_enabled ON sync_tasks(enabled);
CREATE INDEX IF NOT EXISTS idx_sync_tasks_next_sync_time ON sync_tasks(next_sync_time);

CREATE INDEX IF NOT EXISTS idx_sync_records_task_id ON sync_records(task_id);
CREATE INDEX IF NOT EXISTS idx_sync_records_status ON sync_records(status);
CREATE INDEX IF NOT EXISTS idx_sync_records_data_type ON sync_records(data_type);
CREATE INDEX IF NOT EXISTS idx_sync_records_start_time ON sync_records(start_time);

CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

CREATE INDEX IF NOT EXISTS idx_data_sources_type ON data_sources(type);
CREATE INDEX IF NOT EXISTS idx_data_sources_status ON data_sources(status);

CREATE INDEX IF NOT EXISTS idx_operation_logs_user_id ON operation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_operation_logs_operation ON operation_logs(operation);
CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at);

-- 插入默认系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('system.name', '企业数据可视化BI平台', 'STRING', '系统名称'),
('system.version', '1.0.0', 'STRING', '系统版本'),
('sync.default_interval', '300', 'INTEGER', '默认同步间隔（秒）'),
('sync.batch_size', '1000', 'INTEGER', '同步批次大小'),
('sync.retry_count', '3', 'INTEGER', '同步重试次数'),
('data.retention_days', '90', 'INTEGER', '数据保留天数'),
('notification.email_enabled', 'true', 'BOOLEAN', '是否启用邮件通知'),
('notification.sms_enabled', 'false', 'BOOLEAN', '是否启用短信通知')
ON CONFLICT (config_key) DO NOTHING;

-- 插入默认管理员用户
INSERT INTO users (username, password, real_name, email, role, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '系统管理员', '<EMAIL>', 'ADMIN', 'ACTIVE')
ON CONFLICT (username) DO NOTHING;

-- 插入示例同步任务
INSERT INTO sync_tasks (name, description, data_type, sync_config, status, enabled, sync_interval, created_by) VALUES
('销售订单同步', '从金蝶云星辰同步销售订单数据', 'SALES_ORDER', '{"formId":"SAL_SaleOrder","fields":["FBillNo","FDate","FCustId","FAmount"]}', 'ACTIVE', true, 300, 'admin'),
('客户信息同步', '从金蝶云星辰同步客户基础信息', 'CUSTOMER', '{"formId":"BD_Customer","fields":["FNumber","FName","FContact","FPhone"]}', 'ACTIVE', true, 600, 'admin'),
('库存数据同步', '从金蝶云星辰同步库存数据', 'INVENTORY', '{"formId":"STK_Inventory","fields":["FMaterialId","FStockId","FQty"]}', 'ACTIVE', true, 900, 'admin')
ON CONFLICT DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sync_tasks_updated_at BEFORE UPDATE ON sync_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_data_sources_updated_at BEFORE UPDATE ON data_sources FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_data_mappings_updated_at BEFORE UPDATE ON data_mappings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建视图：同步任务统计
CREATE OR REPLACE VIEW v_sync_task_stats AS
SELECT 
    data_type,
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_tasks,
    COUNT(CASE WHEN enabled = true THEN 1 END) as enabled_tasks,
    MAX(last_sync_time) as latest_sync_time
FROM sync_tasks
GROUP BY data_type;

-- 创建视图：同步记录统计
CREATE OR REPLACE VIEW v_sync_record_stats AS
SELECT 
    DATE(start_time) as sync_date,
    data_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_records,
    COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_records,
    SUM(sync_count) as total_sync_count,
    AVG(duration_ms) as avg_duration_ms
FROM sync_records
WHERE start_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(start_time), data_type
ORDER BY sync_date DESC, data_type;

COMMENT ON TABLE users IS '用户表';
COMMENT ON TABLE sync_tasks IS '同步任务表';
COMMENT ON TABLE sync_records IS '同步记录表';
COMMENT ON TABLE data_sources IS '数据源配置表';
COMMENT ON TABLE data_mappings IS '数据映射表';
COMMENT ON TABLE system_configs IS '系统配置表';
COMMENT ON TABLE operation_logs IS '操作日志表';
