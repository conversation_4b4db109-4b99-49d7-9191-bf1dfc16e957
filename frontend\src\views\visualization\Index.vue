<template>
  <div class="visualization-container">
    <div class="page-header">
      <h1 class="page-title">数据可视化</h1>
      <p class="page-description">创建和管理数据可视化图表和仪表板</p>
    </div>

    <div class="visualization-content">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="sidebar">
            <h3>图表类型</h3>
            <div class="chart-types">
              <div 
                class="chart-type-item"
                v-for="type in chartTypes"
                :key="type.id"
                @click="selectChartType(type)"
                :class="{ active: selectedChartType?.id === type.id }"
              >
                <el-icon :size="24">
                  <component :is="type.icon" />
                </el-icon>
                <span>{{ type.name }}</span>
              </div>
            </div>
          </div>
        </el-col>
        
        <el-col :span="18">
          <div class="main-content">
            <div class="toolbar">
              <el-button type="primary" @click="createChart">
                <el-icon><Plus /></el-icon>
                创建图表
              </el-button>
              <el-button @click="saveChart">
                <el-icon><Document /></el-icon>
                保存
              </el-button>
              <el-button @click="exportChart">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
            
            <div class="chart-preview">
              <div v-if="!selectedChartType" class="empty-state">
                <el-icon size="64" color="#C0C4CC">
                  <PieChart />
                </el-icon>
                <p>请选择图表类型开始创建</p>
              </div>
              <div v-else class="chart-container">
                <component :is="selectedChartType.component" :config="chartConfig" />
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

// 图表类型定义
const chartTypes = ref([
  {
    id: 'line',
    name: '折线图',
    icon: 'TrendCharts',
    component: 'LineChart'
  },
  {
    id: 'bar',
    name: '柱状图',
    icon: 'Histogram',
    component: 'BarChart'
  },
  {
    id: 'pie',
    name: '饼图',
    icon: 'PieChart',
    component: 'PieChart'
  },
  {
    id: 'scatter',
    name: '散点图',
    icon: 'ScaleToOriginal',
    component: 'ScatterChart'
  },
  {
    id: 'area',
    name: '面积图',
    icon: 'DataAnalysis',
    component: 'AreaChart'
  },
  {
    id: 'radar',
    name: '雷达图',
    icon: 'Aim',
    component: 'RadarChart'
  }
])

// 选中的图表类型
const selectedChartType = ref<any>(null)

// 图表配置
const chartConfig = reactive({
  title: '示例图表',
  data: [],
  options: {}
})

// 选择图表类型
const selectChartType = (type: any) => {
  selectedChartType.value = type
  ElMessage.success(`已选择${type.name}`)
}

// 创建图表
const createChart = () => {
  if (!selectedChartType.value) {
    ElMessage.warning('请先选择图表类型')
    return
  }
  ElMessage.success('图表创建成功')
}

// 保存图表
const saveChart = () => {
  if (!selectedChartType.value) {
    ElMessage.warning('请先创建图表')
    return
  }
  ElMessage.success('图表保存成功')
}

// 导出图表
const exportChart = () => {
  if (!selectedChartType.value) {
    ElMessage.warning('请先创建图表')
    return
  }
  ElMessage.success('图表导出成功')
}
</script>

<style lang="scss" scoped>
.visualization-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 24px;
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  .page-description {
    color: #909399;
    font-size: 14px;
  }
}

.visualization-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sidebar {
  padding: 24px;
  border-right: 1px solid #f0f0f0;
  height: 600px;
  
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
  }
}

.chart-types {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.chart-type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
  }
  
  &.active {
    border-color: #409eff;
    background-color: #409eff;
    color: white;
  }
  
  span {
    margin-top: 8px;
    font-size: 12px;
  }
}

.main-content {
  padding: 24px;
}

.toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.chart-preview {
  height: 500px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .empty-state {
    text-align: center;
    color: #909399;
    
    p {
      margin-top: 16px;
      font-size: 14px;
    }
  }
  
  .chart-container {
    width: 100%;
    height: 100%;
    padding: 20px;
  }
}
</style>
