// SCSS变量定义

// 颜色系统
$primary-color: #409EFF;
$primary-color-hover: #66B1FF;
$primary-color-active: #3A8EE6;

$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

// 文本颜色
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$text-color-placeholder: #C0C4CC;

// 背景颜色
$bg-color: #F5F7FA;
$bg-color-light: #FAFAFA;
$bg-color-lighter: #FFFFFF;

// 边框颜色
$border-color: #DCDFE6;
$border-color-light: #E4E7ED;
$border-color-lighter: #EBEEF5;
$border-color-extra-light: #F2F6FC;

// 卡片和容器
$card-bg: #FFFFFF;
$table-header-bg: #FAFAFA;
$table-row-hover-bg: #F5F7FA;

// 滚动条
$scrollbar-track-color: #F1F1F1;
$scrollbar-thumb-color: #C1C1C1;
$scrollbar-thumb-hover-color: #A8A8A8;

// 字体
$font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
$font-size-xs: 12px;
$font-size-sm: 13px;
$font-size-base: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 圆角
$border-radius: 4px;
$border-radius-sm: 2px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 阴影
$box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$box-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 12px rgba(0, 0, 0, 0.08);

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-slide: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 布局尺寸
$header-height: 60px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$footer-height: 50px;

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1920px;
