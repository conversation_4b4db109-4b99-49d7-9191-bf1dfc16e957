package com.enterprise.bi.integration.kingdee.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 金蝶API请求对象
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Builder
public class KingdeeRequest {
    
    /**
     * 表单ID
     */
    private String formId;
    
    /**
     * 请求数据
     */
    private Map<String, Object> data;
    
    /**
     * 请求头信息
     */
    private Map<String, String> headers;
    
    /**
     * 请求参数
     */
    private Map<String, Object> parameters;
}
