<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

const chartRef = ref<HTMLElement>()
let chartInstance: ECharts | null = null

// 部门业绩数据
const departmentData = {
  categories: ['销售部', '市场部', '技术部', '客服部', '财务部', '人事部'],
  thisMonth: [850, 620, 450, 380, 320, 280],
  lastMonth: [780, 580, 420, 360, 300, 260]
}

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}万元<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['本月', '上月'],
      top: 10,
      textStyle: {
        color: '#606266'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: departmentData.categories,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#606266',
        interval: 0,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#606266',
        formatter: '{value}万'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f2f5'
        }
      }
    },
    series: [
      {
        name: '本月',
        type: 'bar',
        barWidth: '35%',
        itemStyle: {
          color: '#409EFF',
          borderRadius: [2, 2, 0, 0]
        },
        data: departmentData.thisMonth
      },
      {
        name: '上月',
        type: 'bar',
        barWidth: '35%',
        itemStyle: {
          color: '#E4E7ED',
          borderRadius: [2, 2, 0, 0]
        },
        data: departmentData.lastMonth
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
