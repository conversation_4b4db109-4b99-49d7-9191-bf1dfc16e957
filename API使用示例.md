# 金蝶云星辰API使用示例

## 目录
1. [基础鉴权示例](#基础鉴权示例)
2. [客户信息管理](#客户信息管理)
3. [商品信息管理](#商品信息管理)
4. [前端集成示例](#前端集成示例)
5. [Postman测试集合](#postman测试集合)
6. [错误处理示例](#错误处理示例)

## 基础鉴权示例

### 1. API网关鉴权（推荐）

#### HTTP请求
```http
GET http://localhost:8080/api/auth/access-token-gateway
Content-Type: application/json
```

#### cURL命令
```bash
curl -X GET "http://localhost:8080/api/auth/access-token-gateway" \
     -H "Content-Type: application/json"
```

#### 响应示例
```json
{
  "method": "API Gateway",
  "app_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************.L5whC_JMLXiVEvafP0IsiAOW6ji_dDOHztkFHCzpKd0"
}
```

### 2. 直接API鉴权

#### HTTP请求
```http
GET http://localhost:8080/api/auth/access-token-direct
Content-Type: application/json
```

#### cURL命令
```bash
curl -X GET "http://localhost:8080/api/auth/access-token-direct" \
     -H "Content-Type: application/json"
```

## 客户信息管理

### 1. 获取客户列表

#### HTTP请求
```http
GET http://localhost:8080/api/customers
Content-Type: application/json
```

#### cURL命令
```bash
curl -X GET "http://localhost:8080/api/customers" \
     -H "Content-Type: application/json"
```

#### Java代码示例
```java
@RestController
@RequestMapping("/customers")
public class CustomerController {
    
    @Autowired
    private CustomerService customerService;
    
    @GetMapping
    public ResponseEntity<List<Customer>> getCustomers() {
        try {
            List<Customer> customers = customerService.getCustomers();
            return ResponseEntity.ok(customers);
        } catch (Exception e) {
            log.error("获取客户列表失败", e);
            return ResponseEntity.status(500).body(Collections.emptyList());
        }
    }
    
    @GetMapping("/{customerId}")
    public ResponseEntity<Customer> getCustomer(@PathVariable String customerId) {
        try {
            Customer customer = customerService.getCustomerById(customerId);
            return ResponseEntity.ok(customer);
        } catch (Exception e) {
            log.error("获取客户信息失败: {}", customerId, e);
            return ResponseEntity.notFound().build();
        }
    }
}
```

### 2. 客户查询条件示例

#### 按客户编码查询
```java
@GetMapping("/by-code/{customerCode}")
public ResponseEntity<Customer> getCustomerByCode(@PathVariable String customerCode) {
    try {
        // 构建查询条件
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("FNumber", customerCode);
        
        Customer customer = customerService.getCustomerByCondition(queryParams);
        return ResponseEntity.ok(customer);
    } catch (Exception e) {
        return ResponseEntity.notFound().build();
    }
}
```

#### 按客户名称模糊查询
```java
@GetMapping("/search")
public ResponseEntity<List<Customer>> searchCustomers(@RequestParam String keyword) {
    try {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("FName", "*" + keyword + "*"); // 模糊查询
        
        List<Customer> customers = customerService.searchCustomers(queryParams);
        return ResponseEntity.ok(customers);
    } catch (Exception e) {
        return ResponseEntity.status(500).body(Collections.emptyList());
    }
}
```

## 商品信息管理

### 1. 获取商品列表

#### HTTP请求
```http
GET http://localhost:8080/api/products
Content-Type: application/json
```

#### cURL命令
```bash
curl -X GET "http://localhost:8080/api/products" \
     -H "Content-Type: application/json"
```

#### 响应示例
```json
[
  {
    "id": "001",
    "code": "PROD001",
    "name": "示例商品1",
    "specification": "规格1",
    "unit": "个",
    "price": 100.00,
    "category": "分类A"
  },
  {
    "id": "002", 
    "code": "PROD002",
    "name": "示例商品2",
    "specification": "规格2",
    "unit": "件",
    "price": 200.00,
    "category": "分类B"
  }
]
```

### 2. 商品查询示例

#### 按商品编码查询
```java
@GetMapping("/by-code/{productCode}")
public ResponseEntity<Product> getProductByCode(@PathVariable String productCode) {
    try {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("FNumber", productCode);
        
        Product product = productService.getProductByCondition(queryParams);
        return ResponseEntity.ok(product);
    } catch (Exception e) {
        return ResponseEntity.notFound().build();
    }
}
```

#### 按分类查询商品
```java
@GetMapping("/by-category/{category}")
public ResponseEntity<List<Product>> getProductsByCategory(@PathVariable String category) {
    try {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("FCategoryNumber", category);
        
        List<Product> products = productService.getProductsByCondition(queryParams);
        return ResponseEntity.ok(products);
    } catch (Exception e) {
        return ResponseEntity.status(500).body(Collections.emptyList());
    }
}
```

## 前端集成示例

### 1. JavaScript API调用

#### 基础API客户端
```javascript
class KingdeeApiClient {
    constructor(baseUrl = 'http://localhost:8080/api') {
        this.baseUrl = baseUrl;
        this.token = null;
    }
    
    // 获取访问令牌
    async getAccessToken() {
        try {
            const response = await fetch(`${this.baseUrl}/auth/access-token-gateway`);
            const data = await response.json();
            this.token = data.app_token;
            return this.token;
        } catch (error) {
            console.error('获取访问令牌失败:', error);
            throw error;
        }
    }
    
    // 获取客户列表
    async getCustomers() {
        try {
            const response = await fetch(`${this.baseUrl}/customers`);
            return await response.json();
        } catch (error) {
            console.error('获取客户列表失败:', error);
            throw error;
        }
    }
    
    // 获取商品列表
    async getProducts() {
        try {
            const response = await fetch(`${this.baseUrl}/products`);
            return await response.json();
        } catch (error) {
            console.error('获取商品列表失败:', error);
            throw error;
        }
    }
    
    // 搜索客户
    async searchCustomers(keyword) {
        try {
            const response = await fetch(`${this.baseUrl}/customers/search?keyword=${encodeURIComponent(keyword)}`);
            return await response.json();
        } catch (error) {
            console.error('搜索客户失败:', error);
            throw error;
        }
    }
}
```

#### 使用示例
```javascript
// 初始化API客户端
const apiClient = new KingdeeApiClient();

// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 1. 获取访问令牌
        await apiClient.getAccessToken();
        console.log('访问令牌获取成功');
        
        // 2. 加载客户列表
        const customers = await apiClient.getCustomers();
        displayCustomers(customers);
        
        // 3. 加载商品列表
        const products = await apiClient.getProducts();
        displayProducts(products);
        
    } catch (error) {
        console.error('初始化失败:', error);
        showError('系统初始化失败，请刷新页面重试');
    }
});

// 显示客户列表
function displayCustomers(customers) {
    const container = document.getElementById('customers-container');
    container.innerHTML = customers.map(customer => `
        <div class="customer-item">
            <h3>${customer.name}</h3>
            <p>编码: ${customer.code}</p>
            <p>联系人: ${customer.contact}</p>
        </div>
    `).join('');
}

// 显示商品列表
function displayProducts(products) {
    const container = document.getElementById('products-container');
    container.innerHTML = products.map(product => `
        <div class="product-item">
            <h3>${product.name}</h3>
            <p>编码: ${product.code}</p>
            <p>价格: ¥${product.price}</p>
        </div>
    `).join('');
}

// 客户搜索功能
async function searchCustomers() {
    const keyword = document.getElementById('search-input').value;
    if (!keyword.trim()) return;
    
    try {
        const customers = await apiClient.searchCustomers(keyword);
        displayCustomers(customers);
    } catch (error) {
        showError('搜索失败，请重试');
    }
}
```

### 2. HTML页面示例
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金蝶云星辰API示例</title>
    <style>
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .section { margin-bottom: 30px; }
        .search-box { margin-bottom: 20px; }
        .search-box input { padding: 10px; width: 300px; }
        .search-box button { padding: 10px 20px; }
        .item { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>金蝶云星辰API示例</h1>
        
        <!-- 客户管理 -->
        <div class="section">
            <h2>客户管理</h2>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="输入客户名称搜索">
                <button onclick="searchCustomers()">搜索</button>
            </div>
            <div id="customers-container"></div>
        </div>
        
        <!-- 商品管理 -->
        <div class="section">
            <h2>商品管理</h2>
            <div id="products-container"></div>
        </div>
        
        <!-- 错误提示 -->
        <div id="error-container"></div>
    </div>
    
    <script src="js/api.js"></script>
</body>
</html>
```

## Postman测试集合

### 1. 环境变量设置
```json
{
  "name": "金蝶云星辰API",
  "values": [
    {
      "key": "base_url",
      "value": "http://localhost:8080/api",
      "enabled": true
    },
    {
      "key": "app_token",
      "value": "",
      "enabled": true
    }
  ]
}
```

### 2. 测试用例集合

#### 获取访问令牌
```json
{
  "name": "获取访问令牌",
  "request": {
    "method": "GET",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/json"
      }
    ],
    "url": {
      "raw": "{{base_url}}/auth/access-token-gateway",
      "host": ["{{base_url}}"],
      "path": ["auth", "access-token-gateway"]
    }
  },
  "event": [
    {
      "listen": "test",
      "script": {
        "exec": [
          "pm.test('Status code is 200', function () {",
          "    pm.response.to.have.status(200);",
          "});",
          "",
          "pm.test('Response has app_token', function () {",
          "    var jsonData = pm.response.json();",
          "    pm.expect(jsonData).to.have.property('app_token');",
          "    pm.environment.set('app_token', jsonData.app_token);",
          "});"
        ]
      }
    }
  ]
}
```

#### 获取客户列表
```json
{
  "name": "获取客户列表",
  "request": {
    "method": "GET",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/json"
      }
    ],
    "url": {
      "raw": "{{base_url}}/customers",
      "host": ["{{base_url}}"],
      "path": ["customers"]
    }
  },
  "event": [
    {
      "listen": "test",
      "script": {
        "exec": [
          "pm.test('Status code is 200', function () {",
          "    pm.response.to.have.status(200);",
          "});",
          "",
          "pm.test('Response is array', function () {",
          "    var jsonData = pm.response.json();",
          "    pm.expect(jsonData).to.be.an('array');",
          "});"
        ]
      }
    }
  ]
}
```

## 错误处理示例

### 1. 全局异常处理器
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    @ExceptionHandler(KingdeeApiException.class)
    public ResponseEntity<ErrorResponse> handleKingdeeApiException(KingdeeApiException e) {
        log.error("金蝶API调用异常", e);
        
        ErrorResponse error = ErrorResponse.builder()
            .code("KINGDEE_API_ERROR")
            .message(e.getMessage())
            .timestamp(System.currentTimeMillis())
            .build();
            
        return ResponseEntity.status(500).body(error);
    }
    
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ErrorResponse> handleAuthenticationException(AuthenticationException e) {
        log.error("鉴权异常", e);
        
        ErrorResponse error = ErrorResponse.builder()
            .code("AUTH_ERROR")
            .message("鉴权失败: " + e.getMessage())
            .timestamp(System.currentTimeMillis())
            .build();
            
        return ResponseEntity.status(401).body(error);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception e) {
        log.error("系统异常", e);
        
        ErrorResponse error = ErrorResponse.builder()
            .code("SYSTEM_ERROR")
            .message("系统内部错误")
            .timestamp(System.currentTimeMillis())
            .build();
            
        return ResponseEntity.status(500).body(error);
    }
}
```

### 2. 错误响应模型
```java
@Data
@Builder
public class ErrorResponse {
    private String code;
    private String message;
    private Long timestamp;
    private String path;
    private Map<String, Object> details;
}
```

### 3. 前端错误处理
```javascript
// 统一错误处理函数
function showError(message) {
    const errorContainer = document.getElementById('error-container');
    errorContainer.innerHTML = `<div class="error">${message}</div>`;
    
    // 3秒后自动隐藏错误信息
    setTimeout(() => {
        errorContainer.innerHTML = '';
    }, 3000);
}

// API调用错误处理
async function safeApiCall(apiFunction, errorMessage = '操作失败') {
    try {
        return await apiFunction();
    } catch (error) {
        console.error('API调用失败:', error);
        
        if (error.response) {
            // 服务器返回错误响应
            const errorData = await error.response.json();
            showError(errorData.message || errorMessage);
        } else if (error.request) {
            // 网络错误
            showError('网络连接失败，请检查网络设置');
        } else {
            // 其他错误
            showError(errorMessage);
        }
        
        throw error;
    }
}

// 使用示例
async function loadCustomers() {
    await safeApiCall(
        () => apiClient.getCustomers(),
        '获取客户列表失败'
    );
}
```

## 性能优化建议

### 1. Token缓存
```java
@Component
public class TokenCache {
    private final Cache<String, String> tokenCache = Caffeine.newBuilder()
        .maximumSize(100)
        .expireAfterWrite(50, TimeUnit.MINUTES) // Token有效期通常为1小时，提前10分钟刷新
        .build();
    
    public String getToken(String key) {
        return tokenCache.getIfPresent(key);
    }
    
    public void putToken(String key, String token) {
        tokenCache.put(key, token);
    }
}
```

### 2. 连接池配置
```java
@Configuration
public class HttpClientConfig {
    
    @Bean
    public CloseableHttpClient httpClient() {
        return HttpClients.custom()
            .setMaxConnTotal(200)
            .setMaxConnPerRoute(50)
            .setConnectionTimeToLive(30, TimeUnit.SECONDS)
            .build();
    }
}
```

### 3. 异步处理
```java
@Async
public CompletableFuture<List<Customer>> getCustomersAsync() {
    return CompletableFuture.completedFuture(getCustomers());
}
```

---

通过以上示例，您可以快速上手金蝶云星辰API的开发和集成。建议从基础鉴权开始，逐步实现各个业务功能。