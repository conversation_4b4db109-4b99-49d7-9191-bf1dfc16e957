server:
  port: 8081
  servlet:
    context-path: /api/integration

spring:
  application:
    name: data-integration-service
  
  # 数据库配置
  datasource:
    url: **********************************************
    username: ${DB_USERNAME:bi_user}
    password: ${DB_PASSWORD:bi_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # Kafka配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
    consumer:
      group-id: data-integration-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      properties:
        spring.json.trusted.packages: "*"

# 金蝶云星辰配置
kingdee:
  base-url: ${KINGDEE_BASE_URL:https://api.kingdee.com}
  app-id: ${KINGDEE_APP_ID:}
  app-secret: ${KINGDEE_APP_SECRET:}
  db-id: ${KINGDEE_DB_ID:}
  username: ${KINGDEE_USERNAME:}
  password: ${KINGDEE_PASSWORD:}
  connect-timeout: 30000
  read-timeout: 60000
  retry-count: 3
  retry-interval: 1000
  ssl-enabled: true
  batch-size: 1000
  sync-interval: 300
  incremental-sync-enabled: true
  data-retention-days: 90

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.enterprise.bi: DEBUG
    org.springframework.kafka: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/data-integration-service.log
    max-size: 100MB
    max-history: 30

# 自定义配置
app:
  # 数据处理配置
  data-processing:
    thread-pool-size: 10
    queue-capacity: 1000
    max-retry-attempts: 3
    retry-delay-ms: 1000
  
  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:enterprise-bi-platform-secret-key-2024}
      expiration: 86400000 # 24小时
      refresh-expiration: 604800000 # 7天
  
  # 文件存储配置
  file-storage:
    base-path: ${FILE_STORAGE_PATH:/data/files}
    max-file-size: 100MB
    allowed-extensions: .xlsx,.xls,.csv,.json,.xml
