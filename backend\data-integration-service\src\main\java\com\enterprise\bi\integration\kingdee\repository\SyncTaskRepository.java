package com.enterprise.bi.integration.kingdee.repository;

import com.enterprise.bi.integration.kingdee.entity.SyncTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 同步任务仓库
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Repository
public interface SyncTaskRepository extends JpaRepository<SyncTask, Long> {
    
    /**
     * 根据状态和启用状态查找任务
     */
    List<SyncTask> findByStatusAndEnabled(String status, Boolean enabled);
    
    /**
     * 根据数据类型查找任务
     */
    List<SyncTask> findByDataType(String dataType);
    
    /**
     * 根据数据类型和状态查找任务
     */
    List<SyncTask> findByDataTypeAndStatus(String dataType, String status);
    
    /**
     * 查找需要执行的任务（下次同步时间小于等于当前时间）
     */
    @Query("SELECT t FROM SyncTask t WHERE t.enabled = true AND t.status = 'ACTIVE' " +
           "AND (t.nextSyncTime IS NULL OR t.nextSyncTime <= :currentTime)")
    List<SyncTask> findTasksToExecute(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 根据名称查找任务
     */
    Optional<SyncTask> findByName(String name);
    
    /**
     * 查找启用的任务
     */
    List<SyncTask> findByEnabled(Boolean enabled);
    
    /**
     * 根据创建者查找任务
     */
    List<SyncTask> findByCreatedBy(String createdBy);
    
    /**
     * 统计各状态的任务数量
     */
    @Query("SELECT t.status, COUNT(t) FROM SyncTask t GROUP BY t.status")
    List<Object[]> countByStatus();
    
    /**
     * 统计各数据类型的任务数量
     */
    @Query("SELECT t.dataType, COUNT(t) FROM SyncTask t GROUP BY t.dataType")
    List<Object[]> countByDataType();
}
