server:
  port: 8082
  servlet:
    context-path: /api/processing

spring:
  application:
    name: data-processing-service
  
  # 数据库配置
  datasource:
    url: **********************************************
    username: ${DB_USERNAME:bi_user}
    password: ${DB_PASSWORD:bi_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
  
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 1
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # Kafka配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
    consumer:
      group-id: data-processing-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      properties:
        spring.json.trusted.packages: "*"

# ClickHouse配置
clickhouse:
  url: **********************************************
  username: ${CLICKHOUSE_USERNAME:default}
  password: ${CLICKHOUSE_PASSWORD:}
  driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
  connection-timeout: 30000
  socket-timeout: 60000
  max-connections: 10

# 数据处理引擎配置
data-processing:
  engine:
    # 线程池配置
    thread-pool:
      core-size: 10
      max-size: 50
      queue-capacity: 1000
      keep-alive-seconds: 60
    
    # 批处理配置
    batch:
      size: 1000
      timeout-ms: 30000
      max-retry: 3
    
    # 内存配置
    memory:
      max-heap-size: 2048m
      buffer-size: 64m
    
    # 处理器配置
    processors:
      cleaning:
        enabled: true
        default-config:
          removeDuplicates: true
          keepNullValues: false
          trimStrings: true
          normalizeSpaces: true
      
      transformation:
        enabled: true
        default-config:
          addSystemFields: true
      
      aggregation:
        enabled: true
        default-config:
          aggregationType: "GROUP_BY"
      
      validation:
        enabled: true
        default-config:
          strictMode: false

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    com.enterprise.bi: DEBUG
    org.springframework.kafka: INFO
    org.hibernate.SQL: DEBUG
    org.apache.flink: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/data-processing-service.log
    max-size: 100MB
    max-history: 30

# 自定义配置
app:
  # 数据质量配置
  data-quality:
    enabled: true
    threshold: 0.8
    auto-fix: false
    
  # 实时处理配置
  real-time:
    enabled: true
    window-size: 60s
    watermark-delay: 5s
    
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600
    max-size: 10000
    
  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:enterprise-bi-platform-secret-key-2024}
      expiration: 86400000 # 24小时
      refresh-expiration: 604800000 # 7天
