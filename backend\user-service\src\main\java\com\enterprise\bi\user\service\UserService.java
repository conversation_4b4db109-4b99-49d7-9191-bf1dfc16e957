package com.enterprise.bi.user.service;

import com.enterprise.bi.user.entity.User;
import com.enterprise.bi.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户服务类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService implements UserDetailsService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UsernameNotFoundException("用户不存在: " + username));
        
        log.debug("加载用户: {}", username);
        return user;
    }
    
    /**
     * 创建用户
     */
    @Transactional
    public User createUser(User user) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(user.getUsername())) {
            throw new IllegalArgumentException("用户名已存在: " + user.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (user.getEmail() != null && userRepository.existsByEmail(user.getEmail())) {
            throw new IllegalArgumentException("邮箱已存在: " + user.getEmail());
        }
        
        // 加密密码
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus("ACTIVE");
        }
        if (user.getRole() == null) {
            user.setRole("USER");
        }
        
        User savedUser = userRepository.save(user);
        log.info("创建用户成功: {}", savedUser.getUsername());
        
        return savedUser;
    }
    
    /**
     * 更新用户
     */
    @Transactional
    public User updateUser(Long userId, User userUpdate) {
        User existingUser = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        // 更新基本信息
        if (userUpdate.getRealName() != null) {
            existingUser.setRealName(userUpdate.getRealName());
        }
        if (userUpdate.getEmail() != null) {
            // 检查邮箱是否已被其他用户使用
            if (!existingUser.getEmail().equals(userUpdate.getEmail()) && 
                userRepository.existsByEmail(userUpdate.getEmail())) {
                throw new IllegalArgumentException("邮箱已存在: " + userUpdate.getEmail());
            }
            existingUser.setEmail(userUpdate.getEmail());
        }
        if (userUpdate.getPhone() != null) {
            existingUser.setPhone(userUpdate.getPhone());
        }
        if (userUpdate.getDepartment() != null) {
            existingUser.setDepartment(userUpdate.getDepartment());
        }
        if (userUpdate.getPosition() != null) {
            existingUser.setPosition(userUpdate.getPosition());
        }
        if (userUpdate.getAvatarUrl() != null) {
            existingUser.setAvatarUrl(userUpdate.getAvatarUrl());
        }
        
        User savedUser = userRepository.save(existingUser);
        log.info("更新用户成功: {}", savedUser.getUsername());
        
        return savedUser;
    }
    
    /**
     * 更改密码
     */
    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new IllegalArgumentException("原密码不正确");
        }
        
        // 设置新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        log.info("用户 {} 修改密码成功", user.getUsername());
    }
    
    /**
     * 重置密码
     */
    @Transactional
    public void resetPassword(Long userId, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        log.info("重置用户 {} 密码成功", user.getUsername());
    }
    
    /**
     * 锁定用户
     */
    @Transactional
    public void lockUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        user.setStatus("LOCKED");
        userRepository.save(user);
        
        log.info("锁定用户: {}", user.getUsername());
    }
    
    /**
     * 解锁用户
     */
    @Transactional
    public void unlockUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        user.setStatus("ACTIVE");
        userRepository.save(user);
        
        log.info("解锁用户: {}", user.getUsername());
    }
    
    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在: " + userId));
        
        userRepository.delete(user);
        log.info("删除用户: {}", user.getUsername());
    }
    
    /**
     * 根据ID获取用户
     */
    public Optional<User> getUserById(Long userId) {
        return userRepository.findById(userId);
    }
    
    /**
     * 根据用户名获取用户
     */
    public Optional<User> getUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    /**
     * 根据邮箱获取用户
     */
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    /**
     * 分页获取用户列表
     */
    public Page<User> getUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }
    
    /**
     * 根据状态获取用户列表
     */
    public List<User> getUsersByStatus(String status) {
        return userRepository.findByStatus(status);
    }
    
    /**
     * 根据角色获取用户列表
     */
    public List<User> getUsersByRole(String role) {
        return userRepository.findByRole(role);
    }
    
    /**
     * 根据部门获取用户列表
     */
    public List<User> getUsersByDepartment(String department) {
        return userRepository.findByDepartment(department);
    }
    
    /**
     * 搜索用户
     */
    public Page<User> searchUsers(String keyword, Pageable pageable) {
        return userRepository.findByUsernameContainingOrRealNameContainingOrEmailContaining(
                keyword, keyword, keyword, pageable);
    }
    
    /**
     * 更新最后登录时间
     */
    @Transactional
    public void updateLastLoginTime(String username) {
        userRepository.findByUsername(username).ifPresent(user -> {
            user.setLastLoginAt(LocalDateTime.now());
            userRepository.save(user);
        });
    }
    
    /**
     * 检查用户名是否存在
     */
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }
    
    /**
     * 检查邮箱是否存在
     */
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }
    
    /**
     * 获取用户统计信息
     */
    public UserStatistics getUserStatistics() {
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.countByStatus("ACTIVE");
        long lockedUsers = userRepository.countByStatus("LOCKED");
        long inactiveUsers = userRepository.countByStatus("INACTIVE");
        
        return new UserStatistics(totalUsers, activeUsers, lockedUsers, inactiveUsers);
    }
    
    /**
     * 用户统计信息内部类
     */
    public static class UserStatistics {
        private final long totalUsers;
        private final long activeUsers;
        private final long lockedUsers;
        private final long inactiveUsers;
        
        public UserStatistics(long totalUsers, long activeUsers, long lockedUsers, long inactiveUsers) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.lockedUsers = lockedUsers;
            this.inactiveUsers = inactiveUsers;
        }
        
        // Getters
        public long getTotalUsers() { return totalUsers; }
        public long getActiveUsers() { return activeUsers; }
        public long getLockedUsers() { return lockedUsers; }
        public long getInactiveUsers() { return inactiveUsers; }
    }
}
