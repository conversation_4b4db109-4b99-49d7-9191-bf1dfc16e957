import request from '@/utils/request'
import type { 
  LoginForm, 
  LoginResponse, 
  User, 
  UserPermission, 
  ChangePasswordForm,
  UserProfile,
  ApiResponse 
} from '@/types/user'

// 认证相关API
export const authApi = {
  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return request.post('/auth/login', data)
  },

  // 用户登出
  logout(): Promise<ApiResponse> {
    return request.post('/auth/logout')
  },

  // 获取用户信息
  getUserInfo(): Promise<ApiResponse<User>> {
    return request.get('/auth/user/info')
  },

  // 获取用户权限
  getUserPermissions(): Promise<ApiResponse<UserPermission[]>> {
    return request.get('/auth/user/permissions')
  },

  // 刷新Token
  refreshToken(): Promise<ApiResponse<{ token: string }>> {
    return request.post('/auth/refresh-token')
  },

  // 修改密码
  changePassword(data: ChangePasswordForm): Promise<ApiResponse> {
    return request.post('/auth/change-password', data)
  },

  // 更新用户信息
  updateUserInfo(data: Partial<UserProfile>): Promise<ApiResponse<User>> {
    return request.put('/auth/user/profile', data)
  },

  // 上传头像
  uploadAvatar(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('avatar', file)
    return request.post('/auth/user/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取验证码
  getCaptcha(): Promise<ApiResponse<{ captcha: string; key: string }>> {
    return request.get('/auth/captcha')
  },

  // 发送短信验证码
  sendSmsCode(phone: string): Promise<ApiResponse> {
    return request.post('/auth/sms-code', { phone })
  },

  // 发送邮箱验证码
  sendEmailCode(email: string): Promise<ApiResponse> {
    return request.post('/auth/email-code', { email })
  },

  // 重置密码
  resetPassword(data: {
    username: string
    code: string
    newPassword: string
    type: 'sms' | 'email'
  }): Promise<ApiResponse> {
    return request.post('/auth/reset-password', data)
  },

  // 绑定第三方账号
  bindThirdParty(data: {
    platform: 'dingtalk' | 'wechat' | 'feishu'
    code: string
  }): Promise<ApiResponse> {
    return request.post('/auth/bind-third-party', data)
  },

  // 解绑第三方账号
  unbindThirdParty(platform: 'dingtalk' | 'wechat' | 'feishu'): Promise<ApiResponse> {
    return request.delete(`/auth/unbind-third-party/${platform}`)
  }
}
