package com.enterprise.bi.visualization.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 仪表板实体类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "dashboards")
@EqualsAndHashCode(callSuper = false)
public class Dashboard {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 仪表板名称
     */
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    /**
     * 仪表板描述
     */
    @Column(name = "description", length = 1000)
    private String description;
    
    /**
     * 仪表板类型：OVERVIEW, SALES, FINANCE, OPERATION
     */
    @Column(name = "type", nullable = false, length = 50)
    private String type;
    
    /**
     * 仪表板布局配置（JSON格式）
     */
    @Column(name = "layout_config", columnDefinition = "TEXT")
    private String layoutConfig;
    
    /**
     * 仪表板样式配置（JSON格式）
     */
    @Column(name = "style_config", columnDefinition = "TEXT")
    private String styleConfig;
    
    /**
     * 是否公开
     */
    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;
    
    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 访问次数
     */
    @Column(name = "view_count")
    private Long viewCount = 0L;
    
    /**
     * 最后访问时间
     */
    @Column(name = "last_viewed_at")
    private LocalDateTime lastViewedAt;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * 仪表板组件关联
     */
    @OneToMany(mappedBy = "dashboard", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<DashboardWidget> widgets;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
