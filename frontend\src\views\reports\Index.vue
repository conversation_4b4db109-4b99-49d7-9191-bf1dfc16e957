<template>
  <div class="reports-container">
    <div class="page-header">
      <h1 class="page-title">报表管理</h1>
      <p class="page-description">创建、管理和查看各类业务报表</p>
    </div>

    <div class="reports-content">
      <div class="content-header">
        <el-button type="primary" @click="showCreateReport = true">
          <el-icon><Plus /></el-icon>
          创建报表
        </el-button>
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索报表..."
            style="width: 300px"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <div class="reports-grid">
        <div 
          class="report-card"
          v-for="report in filteredReports"
          :key="report.id"
          @click="viewReport(report.id)"
        >
          <div class="report-header">
            <div class="report-icon">
              <el-icon :size="24" :color="report.iconColor">
                <component :is="report.icon" />
              </el-icon>
            </div>
            <div class="report-actions">
              <el-dropdown @command="handleReportAction">
                <el-icon><MoreFilled /></el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="`view-${report.id}`">查看</el-dropdown-item>
                    <el-dropdown-item :command="`edit-${report.id}`">编辑</el-dropdown-item>
                    <el-dropdown-item :command="`export-${report.id}`">导出</el-dropdown-item>
                    <el-dropdown-item :command="`delete-${report.id}`" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <div class="report-content">
            <h3 class="report-title">{{ report.title }}</h3>
            <p class="report-description">{{ report.description }}</p>
            
            <div class="report-meta">
              <div class="meta-item">
                <span class="meta-label">类型:</span>
                <span class="meta-value">{{ report.type }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">更新:</span>
                <span class="meta-value">{{ formatTime(report.updatedAt) }}</span>
              </div>
            </div>
          </div>
          
          <div class="report-footer">
            <el-tag :type="getStatusType(report.status)" size="small">
              {{ getStatusText(report.status) }}
            </el-tag>
            <span class="report-author">{{ report.author }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建报表对话框 -->
    <el-dialog
      v-model="showCreateReport"
      title="创建报表"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="reportForm" :rules="reportRules" ref="reportFormRef" label-width="100px">
        <el-form-item label="报表名称" prop="title">
          <el-input v-model="reportForm.title" placeholder="请输入报表名称" />
        </el-form-item>
        <el-form-item label="报表类型" prop="type">
          <el-select v-model="reportForm.type" placeholder="请选择报表类型" style="width: 100%">
            <el-option label="销售报表" value="sales" />
            <el-option label="财务报表" value="finance" />
            <el-option label="库存报表" value="inventory" />
            <el-option label="客户报表" value="customer" />
            <el-option label="自定义报表" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="dataSource">
          <el-select v-model="reportForm.dataSource" placeholder="请选择数据源" style="width: 100%">
            <el-option label="金蝶云星辰" value="kingdee" />
            <el-option label="本地数据库" value="local" />
            <el-option label="Excel文件" value="excel" />
          </el-select>
        </el-form-item>
        <el-form-item label="报表描述" prop="description">
          <el-input
            v-model="reportForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入报表描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateReport = false">取消</el-button>
        <el-button type="primary" @click="createReport">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { formatTime } from '@/utils/date'

// 表单引用
const reportFormRef = ref<FormInstance>()

// 状态
const showCreateReport = ref(false)
const searchKeyword = ref('')

// 报表列表
const reports = ref([
  {
    id: 1,
    title: '月度销售报表',
    description: '展示本月销售业绩和趋势分析',
    type: '销售报表',
    status: 'published',
    author: '张三',
    icon: 'TrendCharts',
    iconColor: '#409EFF',
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 2,
    title: '财务收支明细',
    description: '详细的财务收支情况统计',
    type: '财务报表',
    status: 'draft',
    author: '李四',
    icon: 'Money',
    iconColor: '#67C23A',
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    id: 3,
    title: '库存预警报表',
    description: '库存不足商品预警和补货建议',
    type: '库存报表',
    status: 'published',
    author: '王五',
    icon: 'Box',
    iconColor: '#E6A23C',
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
  },
  {
    id: 4,
    title: '客户分析报告',
    description: '客户行为分析和价值评估',
    type: '客户报表',
    status: 'published',
    author: '赵六',
    icon: 'User',
    iconColor: '#F56C6C',
    updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
  }
])

// 报表表单
const reportForm = reactive({
  title: '',
  type: '',
  dataSource: '',
  description: ''
})

// 表单验证规则
const reportRules: FormRules = {
  title: [
    { required: true, message: '请输入报表名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择报表类型', trigger: 'change' }
  ],
  dataSource: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ]
}

// 过滤后的报表列表
const filteredReports = computed(() => {
  if (!searchKeyword.value) {
    return reports.value
  }
  return reports.value.filter(report =>
    report.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    report.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 获取状态类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    published: 'success',
    draft: 'warning',
    archived: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    published: '已发布',
    draft: '草稿',
    archived: '已归档'
  }
  return statusMap[status] || '未知'
}

// 查看报表
const viewReport = (reportId: number) => {
  ElMessage.info(`查看报表 ${reportId}`)
}

// 处理报表操作
const handleReportAction = (command: string) => {
  const [action, id] = command.split('-')
  const reportId = parseInt(id)
  
  switch (action) {
    case 'view':
      viewReport(reportId)
      break
    case 'edit':
      ElMessage.info(`编辑报表 ${reportId}`)
      break
    case 'export':
      ElMessage.info(`导出报表 ${reportId}`)
      break
    case 'delete':
      deleteReport(reportId)
      break
  }
}

// 删除报表
const deleteReport = async (reportId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个报表吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('报表删除成功')
  } catch (error) {
    // 用户取消
  }
}

// 创建报表
const createReport = async () => {
  if (!reportFormRef.value) return
  
  try {
    await reportFormRef.value.validate()
    ElMessage.success('报表创建成功')
    showCreateReport.value = false
    
    // 重置表单
    Object.assign(reportForm, {
      title: '',
      type: '',
      dataSource: '',
      description: ''
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.reports-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 24px;
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  .page-description {
    color: #909399;
    font-size: 14px;
  }
}

.reports-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.report-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
  
  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .report-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: rgba(64, 158, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .report-actions {
      cursor: pointer;
      color: #909399;
      
      &:hover {
        color: #409eff;
      }
    }
  }
  
  .report-content {
    margin-bottom: 16px;
    
    .report-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }
    
    .report-description {
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
      margin-bottom: 12px;
    }
    
    .report-meta {
      display: flex;
      gap: 16px;
      
      .meta-item {
        font-size: 12px;
        
        .meta-label {
          color: #909399;
        }
        
        .meta-value {
          color: #606266;
          margin-left: 4px;
        }
      }
    }
  }
  
  .report-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .report-author {
      font-size: 12px;
      color: #909399;
    }
  }
}

@media (max-width: 768px) {
  .reports-container {
    padding: 16px;
  }
  
  .content-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .reports-grid {
    grid-template-columns: 1fr;
  }
}
</style>
