package com.enterprise.bi.processing.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

/**
 * 数据转换处理器
 * 负责数据格式转换、字段映射、数据类型转换等操作
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class DataTransformationProcessor implements DataProcessor {
    
    @Override
    public Object process(Object data, Map<String, Object> config) {
        log.debug("开始执行数据转换处理");
        
        if (!validateInput(data)) {
            throw new IllegalArgumentException("输入数据格式不正确");
        }
        
        if (data instanceof List) {
            return processListData((List<?>) data, config);
        } else if (data instanceof Map) {
            return processMapData((Map<?, ?>) data, config);
        }
        
        return data;
    }
    
    /**
     * 处理列表数据
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> processListData(List<?> dataList, Map<String, Object> config) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Object item : dataList) {
            if (item instanceof Map) {
                Map<String, Object> transformedItem = transformMapData((Map<String, Object>) item, config);
                result.add(transformedItem);
            }
        }
        
        log.debug("数据转换完成，处理数据量: {}", result.size());
        return result;
    }
    
    /**
     * 处理Map数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> processMapData(Map<?, ?> dataMap, Map<String, Object> config) {
        return transformMapData((Map<String, Object>) dataMap, config);
    }
    
    /**
     * 转换Map数据
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> transformMapData(Map<String, Object> dataMap, Map<String, Object> config) {
        Map<String, Object> transformedMap = new HashMap<>();
        
        // 获取字段映射配置
        Map<String, String> fieldMappings = (Map<String, String>) config.get("fieldMappings");
        if (fieldMappings == null) {
            fieldMappings = new HashMap<>();
        }
        
        // 获取数据类型转换配置
        Map<String, String> typeConversions = (Map<String, String>) config.get("typeConversions");
        if (typeConversions == null) {
            typeConversions = new HashMap<>();
        }
        
        // 获取计算字段配置
        Map<String, String> calculatedFields = (Map<String, String>) config.get("calculatedFields");
        if (calculatedFields == null) {
            calculatedFields = new HashMap<>();
        }
        
        // 处理原有字段
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String originalKey = entry.getKey();
            Object value = entry.getValue();
            
            // 应用字段映射
            String targetKey = fieldMappings.getOrDefault(originalKey, originalKey);
            
            // 应用数据类型转换
            if (typeConversions.containsKey(originalKey)) {
                value = convertDataType(value, typeConversions.get(originalKey));
            }
            
            transformedMap.put(targetKey, value);
        }
        
        // 添加计算字段
        for (Map.Entry<String, String> entry : calculatedFields.entrySet()) {
            String fieldName = entry.getKey();
            String expression = entry.getValue();
            Object calculatedValue = calculateField(transformedMap, expression);
            transformedMap.put(fieldName, calculatedValue);
        }
        
        // 添加系统字段
        if (getBooleanConfig(config, "addSystemFields", false)) {
            addSystemFields(transformedMap);
        }
        
        return transformedMap;
    }
    
    /**
     * 数据类型转换
     */
    private Object convertDataType(Object value, String targetType) {
        if (value == null) {
            return null;
        }
        
        try {
            switch (targetType.toUpperCase()) {
                case "STRING":
                    return String.valueOf(value);
                    
                case "INTEGER":
                    if (value instanceof Number) {
                        return ((Number) value).intValue();
                    }
                    return Integer.parseInt(value.toString());
                    
                case "LONG":
                    if (value instanceof Number) {
                        return ((Number) value).longValue();
                    }
                    return Long.parseLong(value.toString());
                    
                case "DOUBLE":
                    if (value instanceof Number) {
                        return ((Number) value).doubleValue();
                    }
                    return Double.parseDouble(value.toString());
                    
                case "BOOLEAN":
                    if (value instanceof Boolean) {
                        return value;
                    }
                    String strValue = value.toString().toLowerCase();
                    return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue);
                    
                case "DATE":
                    return convertToDate(value);
                    
                default:
                    log.warn("不支持的数据类型转换: {}", targetType);
                    return value;
            }
        } catch (Exception e) {
            log.warn("数据类型转换失败: {} -> {}, 值: {}", value.getClass().getSimpleName(), targetType, value);
            return value;
        }
    }
    
    /**
     * 转换为日期
     */
    private Object convertToDate(Object value) {
        if (value instanceof String) {
            String dateStr = (String) value;
            // 尝试多种日期格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy/MM/dd",
                "dd/MM/yyyy",
                "dd-MM-yyyy"
            };
            
            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    return LocalDateTime.parse(dateStr, formatter);
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }
        }
        return value;
    }
    
    /**
     * 计算字段
     */
    private Object calculateField(Map<String, Object> dataMap, String expression) {
        // 简单的表达式计算实现
        // 支持基本的数学运算和字段引用
        
        try {
            // 替换字段引用
            String processedExpression = expression;
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                String fieldName = entry.getKey();
                Object value = entry.getValue();
                if (value instanceof Number) {
                    processedExpression = processedExpression.replace("${" + fieldName + "}", value.toString());
                }
            }
            
            // 执行简单的数学计算
            return evaluateExpression(processedExpression);
            
        } catch (Exception e) {
            log.warn("计算字段失败: {}", expression, e);
            return null;
        }
    }
    
    /**
     * 简单表达式求值
     */
    private Object evaluateExpression(String expression) {
        // 这里实现简单的数学表达式求值
        // 实际项目中可以使用更强大的表达式引擎如SpEL或MVEL
        
        expression = expression.trim();
        
        // 处理加法
        if (expression.contains("+")) {
            String[] parts = expression.split("\\+");
            double result = 0;
            for (String part : parts) {
                result += Double.parseDouble(part.trim());
            }
            return result;
        }
        
        // 处理减法
        if (expression.contains("-") && !expression.startsWith("-")) {
            String[] parts = expression.split("-");
            double result = Double.parseDouble(parts[0].trim());
            for (int i = 1; i < parts.length; i++) {
                result -= Double.parseDouble(parts[i].trim());
            }
            return result;
        }
        
        // 处理乘法
        if (expression.contains("*")) {
            String[] parts = expression.split("\\*");
            double result = 1;
            for (String part : parts) {
                result *= Double.parseDouble(part.trim());
            }
            return result;
        }
        
        // 处理除法
        if (expression.contains("/")) {
            String[] parts = expression.split("/");
            double result = Double.parseDouble(parts[0].trim());
            for (int i = 1; i < parts.length; i++) {
                result /= Double.parseDouble(parts[i].trim());
            }
            return result;
        }
        
        // 直接返回数值
        try {
            return Double.parseDouble(expression);
        } catch (NumberFormatException e) {
            return expression;
        }
    }
    
    /**
     * 添加系统字段
     */
    private void addSystemFields(Map<String, Object> dataMap) {
        dataMap.put("_processedAt", LocalDateTime.now());
        dataMap.put("_processorVersion", "1.0.0");
        dataMap.put("_recordId", UUID.randomUUID().toString());
    }
    
    /**
     * 获取布尔配置
     */
    private boolean getBooleanConfig(Map<String, Object> config, String key, boolean defaultValue) {
        Object value = config.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }
    
    @Override
    public String getProcessorName() {
        return "DataTransformationProcessor";
    }
    
    @Override
    public String getProcessorDescription() {
        return "数据转换处理器，负责数据格式转换、字段映射、数据类型转换等操作";
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data instanceof List || data instanceof Map;
    }
    
    @Override
    public Class<?>[] getSupportedDataTypes() {
        return new Class<?>[]{List.class, Map.class};
    }
}
