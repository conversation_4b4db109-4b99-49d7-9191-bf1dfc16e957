package com.enterprise.bi.integration.kingdee.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 金蝶云星辰配置类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "kingdee")
public class KingdeeConfig {
    
    /**
     * 金蝶云星辰API基础URL
     */
    private String baseUrl = "https://api.kingdee.com";
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 应用密钥
     */
    private String appSecret;
    
    /**
     * 数据库ID
     */
    private String dbId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;
    
    /**
     * 重试次数
     */
    private int retryCount = 3;
    
    /**
     * 重试间隔（毫秒）
     */
    private int retryInterval = 1000;
    
    /**
     * 是否启用SSL验证
     */
    private boolean sslEnabled = true;
    
    /**
     * 数据同步批次大小
     */
    private int batchSize = 1000;
    
    /**
     * 数据同步间隔（秒）
     */
    private int syncInterval = 300;
    
    /**
     * 是否启用增量同步
     */
    private boolean incrementalSyncEnabled = true;
    
    /**
     * 数据保留天数
     */
    private int dataRetentionDays = 90;
}
