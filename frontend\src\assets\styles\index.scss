// 全局样式文件

// 导入变量和混入
@import './variables.scss';
@import './mixins.scss';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: $font-family;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: $bg-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: $primary-color;
  text-decoration: none;
  
  &:hover {
    color: $primary-color-hover;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $scrollbar-track-color;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: $scrollbar-thumb-color;
  border-radius: 4px;
  
  &:hover {
    background: $scrollbar-thumb-hover-color;
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}
.flex-column { 
  display: flex; 
  flex-direction: column; 
}

.w-full { width: 100%; }
.h-full { height: 100%; }

.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }

.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }
.ml-5 { margin-left: 20px; }

.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }
.mr-5 { margin-right: 20px; }

.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }

// 卡片样式
.card {
  background: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
  
  &.card-hover {
    transition: box-shadow 0.3s ease;
    
    &:hover {
      box-shadow: $box-shadow-hover;
    }
  }
}

// 页面容器
.page-container {
  padding: $spacing-lg;
  min-height: calc(100vh - 60px);
  
  .page-header {
    margin-bottom: $spacing-lg;
    
    .page-title {
      font-size: $font-size-xl;
      font-weight: 600;
      color: $text-color-primary;
      margin-bottom: $spacing-sm;
    }
    
    .page-description {
      color: $text-color-secondary;
      font-size: $font-size-sm;
    }
  }
}

// 表格样式增强
.el-table {
  .el-table__header {
    th {
      background-color: $table-header-bg;
      color: $text-color-primary;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: $table-row-hover-bg;
    }
  }
}

// 表单样式增强
.el-form {
  .el-form-item__label {
    color: $text-color-primary;
    font-weight: 500;
  }
}

// 按钮样式增强
.el-button {
  &.el-button--primary {
    background-color: $primary-color;
    border-color: $primary-color;
    
    &:hover {
      background-color: $primary-color-hover;
      border-color: $primary-color-hover;
    }
  }
}

// 加载状态
.loading-container {
  position: relative;
  min-height: 200px;
  
  .loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: $spacing-xl 0;
  color: $text-color-secondary;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: $spacing-md;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: $font-size-base;
    margin-bottom: $spacing-sm;
  }
  
  .empty-description {
    font-size: $font-size-sm;
    color: $text-color-placeholder;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-container {
    padding: $spacing-md;
  }
  
  .card {
    padding: $spacing-md;
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  .page-container {
    padding: 0;
  }
}
