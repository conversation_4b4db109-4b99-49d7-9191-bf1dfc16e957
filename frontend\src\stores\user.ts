import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginForm, UserPermission } from '@/types/user'
import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const permissions = ref<UserPermission[]>([])
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userInfo = computed(() => user.value)
  const userPermissions = computed(() => permissions.value)

  // 初始化用户信息
  const initUserInfo = async () => {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
      try {
        await getUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        logout()
      }
    }
  }

  // 登录
  const login = async (loginForm: LoginForm) => {
    loading.value = true
    try {
      const response = await authApi.login(loginForm)
      const { token: userToken, user: userInfo } = response.data
      
      token.value = userToken
      user.value = userInfo
      
      // 保存到本地存储
      localStorage.setItem('token', userToken)
      localStorage.setItem('user', JSON.stringify(userInfo))
      
      // 获取用户权限
      await getUserPermissions()
      
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      ElMessage.error('登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      user.value = null
      permissions.value = []
      
      // 清除本地存储
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('permissions')
      
      ElMessage.success('已退出登录')
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await authApi.getUserInfo()
      user.value = response.data
      localStorage.setItem('user', JSON.stringify(response.data))
      return response.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 获取用户权限
  const getUserPermissions = async () => {
    try {
      const response = await authApi.getUserPermissions()
      permissions.value = response.data
      localStorage.setItem('permissions', JSON.stringify(response.data))
      return response.data
    } catch (error) {
      console.error('获取用户权限失败:', error)
      throw error
    }
  }

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.some(p => p.code === permission)
  }

  // 检查多个权限
  const hasPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }

  // 检查角色
  const hasRole = (role: string): boolean => {
    return user.value?.roles?.some(r => r.code === role) || false
  }

  // 更新用户信息
  const updateUserInfo = async (userInfo: Partial<User>) => {
    try {
      const response = await authApi.updateUserInfo(userInfo)
      user.value = { ...user.value, ...response.data }
      localStorage.setItem('user', JSON.stringify(user.value))
      ElMessage.success('更新成功')
      return response.data
    } catch (error) {
      ElMessage.error('更新失败')
      throw error
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      await authApi.changePassword({ oldPassword, newPassword })
      ElMessage.success('密码修改成功')
    } catch (error) {
      ElMessage.error('密码修改失败')
      throw error
    }
  }

  return {
    // 状态
    user,
    token,
    permissions,
    loading,
    
    // 计算属性
    isLoggedIn,
    userInfo,
    userPermissions,
    
    // 方法
    initUserInfo,
    login,
    logout,
    getUserInfo,
    getUserPermissions,
    hasPermission,
    hasPermissions,
    hasRole,
    updateUserInfo,
    changePassword
  }
})
