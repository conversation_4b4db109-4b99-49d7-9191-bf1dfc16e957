package com.enterprise.bi.processing.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据处理任务模型
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingTask {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 数据源类型
     */
    private String dataSourceType;
    
    /**
     * 输入数据
     */
    private Object data;
    
    /**
     * 处理步骤列表
     */
    private List<String> processingSteps;
    
    /**
     * 处理配置
     */
    private Map<String, Object> processingConfig;
    
    /**
     * 任务优先级
     */
    private Integer priority;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 预期完成时间
     */
    private LocalDateTime expectedCompletionTime;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 任务标签
     */
    private Map<String, String> tags;
    
    /**
     * 是否异步执行
     */
    private Boolean async;
    
    /**
     * 回调URL
     */
    private String callbackUrl;
}
