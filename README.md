# 企业数据可视化BI平台

## 项目概述

企业数据可视化BI平台是一个可扩展的企业运营数据集成与可视化系统，旨在解决传统管理软件流程不透明、状态分散的问题，通过直观的可视化展示提升业务透明度和管理效率。

## 技术架构

### 整体架构
- **接入层**: API Gateway、负载均衡
- **服务层**: 业务微服务、数据服务  
- **数据层**: 数据存储、缓存、消息队列
- **基础设施层**: 容器平台、监控、日志

### 技术栈
- **前端**: Vue3 + TypeScript + Element Plus
- **后端**: Spring Boot + Java 17
- **数据库**: PostgreSQL + Redis + ClickHouse
- **消息队列**: Apache Kafka
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana

## 项目结构

```
enterprise-bi-platform/
├── frontend/                    # 前端Vue3项目
│   ├── src/
│   │   ├── components/         # 可视化组件库
│   │   ├── views/             # 页面视图
│   │   ├── api/               # API接口
│   │   └── utils/             # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── backend/                     # 后端微服务
│   ├── gateway-service/        # API网关服务
│   ├── auth-service/           # 认证授权服务
│   ├── data-integration-service/ # 数据集成服务
│   ├── data-processing-service/  # 数据处理服务
│   ├── visualization-service/    # 可视化服务
│   ├── notification-service/     # 消息通知服务
│   └── common/                  # 公共模块
├── database/                    # 数据库脚本
│   ├── postgresql/             # PostgreSQL脚本
│   └── migrations/             # 数据库迁移脚本
├── deployment/                  # 部署配置
│   ├── docker/                 # Docker配置
│   ├── kubernetes/             # K8s配置
│   └── ci-cd/                  # CI/CD配置
├── docs/                       # 项目文档
│   ├── api/                    # API文档
│   ├── architecture/           # 架构文档
│   └── deployment/             # 部署文档
└── scripts/                    # 脚本工具
    ├── build.sh               # 构建脚本
    ├── deploy.sh              # 部署脚本
    └── init-env.sh            # 环境初始化脚本
```

## 核心功能模块

### 1. 数据集成模块
- 金蝶云星辰系统集成
- 多平台消息集成（钉钉、企业微信、飞书）
- 实时数据同步和批量数据导入

### 2. 数据处理模块  
- 数据清洗与转换
- 实时计算引擎
- 预警规则引擎

### 3. 可视化展示模块
- 仪表板管理
- 单据流可视化
- 多种图表类型支持

### 4. 用户管理模块
- 基于RBAC的权限管理
- 单点登录(SSO)支持
- 个性化设置

### 5. 报表与分析模块
- 报表生成器
- OLAP多维分析
- 数据挖掘和预测

## 开发环境要求

### 基础环境
- Node.js 18+
- Java 17+
- PostgreSQL 14+
- Redis 6+
- Docker 20+

### 开发工具
- IDE: IntelliJ IDEA / VS Code
- 版本控制: Git
- 构建工具: Maven / Vite
- 容器: Docker Desktop

## 快速开始

### 1. 环境初始化
```bash
# 克隆项目
git clone <repository-url>
cd enterprise-bi-platform

# 初始化环境
./scripts/init-env.sh
```

### 2. 启动后端服务
```bash
cd backend
# 启动各个微服务
./mvnw spring-boot:run -pl gateway-service
./mvnw spring-boot:run -pl auth-service
./mvnw spring-boot:run -pl data-integration-service
```

### 3. 启动前端项目
```bash
cd frontend
npm install
npm run dev
```

### 4. 访问系统
- 前端地址: http://localhost:3000
- API网关: http://localhost:8080
- 监控面板: http://localhost:9090

## 开发规范

### 代码规范
- 前端: ESLint + Prettier
- 后端: Checkstyle + SpotBugs
- 代码覆盖率: ≥ 80%

### 提交规范
- 使用 Conventional Commits 规范
- 每次提交必须通过CI检查
- 代码审查通过后方可合并

### 测试规范
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖核心业务流程
- 性能测试满足需求指标

## 部署说明

### 开发环境
```bash
docker-compose -f deployment/docker/docker-compose.dev.yml up -d
```

### 生产环境
```bash
kubectl apply -f deployment/kubernetes/
```

## 监控与运维

### 监控指标
- 应用性能监控(APM)
- 业务指标监控
- 基础设施监控

### 日志管理
- 集中式日志收集
- 日志分析和告警
- 审计日志追踪

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交代码变更
4. 创建 Pull Request
5. 代码审查和合并

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目经理: [待填写]
- 技术负责人: [待填写]
- 邮箱: [待填写]

## 更新日志

### v1.0.0 (2025-09-22)
- 项目初始化
- 基础架构搭建
- 核心模块开发启动
