package com.enterprise.bi.integration.kingdee.client;

import com.enterprise.bi.integration.kingdee.config.KingdeeConfig;
import com.enterprise.bi.integration.kingdee.dto.KingdeeRequest;
import com.enterprise.bi.integration.kingdee.dto.KingdeeResponse;
import com.enterprise.bi.integration.kingdee.exception.KingdeeApiException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Map;

/**
 * 金蝶云星辰API客户端
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KingdeeApiClient {
    
    private final KingdeeConfig kingdeeConfig;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    private String accessToken;
    private long tokenExpireTime;
    
    /**
     * 执行API请求
     * 
     * @param request 请求对象
     * @return 响应对象
     */
    public Mono<KingdeeResponse> executeRequest(KingdeeRequest request) {
        return ensureAuthenticated()
                .then(performRequest(request))
                .retryWhen(Retry.backoff(kingdeeConfig.getRetryCount(), 
                          Duration.ofMillis(kingdeeConfig.getRetryInterval()))
                          .filter(this::isRetryableException))
                .onErrorMap(this::mapException);
    }
    
    /**
     * 确保已认证
     */
    private Mono<Void> ensureAuthenticated() {
        if (isTokenValid()) {
            return Mono.empty();
        }
        return authenticate();
    }
    
    /**
     * 检查Token是否有效
     */
    private boolean isTokenValid() {
        return accessToken != null && System.currentTimeMillis() < tokenExpireTime;
    }
    
    /**
     * 执行认证
     */
    private Mono<Void> authenticate() {
        log.info("开始金蝶云星辰认证...");
        
        Map<String, Object> authRequest = Map.of(
            "acct_id", kingdeeConfig.getDbId(),
            "username", kingdeeConfig.getUsername(),
            "password", kingdeeConfig.getPassword(),
            "lcid", 2052
        );
        
        return webClient.post()
                .uri(kingdeeConfig.getBaseUrl() + "/K3Cloud/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc")
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(authRequest)
                .retrieve()
                .bodyToMono(Map.class)
                .doOnNext(response -> {
                    log.info("金蝶云星辰认证响应: {}", response);
                    if (response.containsKey("SessionId")) {
                        this.accessToken = (String) response.get("SessionId");
                        // Token有效期设置为2小时
                        this.tokenExpireTime = System.currentTimeMillis() + 2 * 60 * 60 * 1000;
                        log.info("金蝶云星辰认证成功，Token: {}", accessToken);
                    } else {
                        throw new KingdeeApiException("认证失败: " + response);
                    }
                })
                .then()
                .doOnError(error -> log.error("金蝶云星辰认证失败", error));
    }
    
    /**
     * 执行具体请求
     */
    private Mono<KingdeeResponse> performRequest(KingdeeRequest request) {
        return webClient.post()
                .uri(buildRequestUrl(request))
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header("Cookie", "kdservice-sessionid=" + accessToken)
                .bodyValue(request.getData())
                .retrieve()
                .bodyToMono(String.class)
                .map(responseBody -> {
                    try {
                        return objectMapper.readValue(responseBody, KingdeeResponse.class);
                    } catch (Exception e) {
                        throw new KingdeeApiException("响应解析失败", e);
                    }
                })
                .doOnNext(response -> {
                    if (!response.isSuccess()) {
                        throw new KingdeeApiException("API调用失败: " + response.getMessage());
                    }
                });
    }
    
    /**
     * 构建请求URL
     */
    private String buildRequestUrl(KingdeeRequest request) {
        return String.format("%s/K3Cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.%s.common.kdsvc",
                kingdeeConfig.getBaseUrl(), request.getFormId());
    }
    
    /**
     * 判断是否为可重试的异常
     */
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            HttpStatus status = (HttpStatus) ex.getStatusCode();
            return status.is5xxServerError() || 
                   status == HttpStatus.REQUEST_TIMEOUT ||
                   status == HttpStatus.TOO_MANY_REQUESTS;
        }
        return throwable instanceof java.net.ConnectException ||
               throwable instanceof java.net.SocketTimeoutException;
    }
    
    /**
     * 映射异常
     */
    private Throwable mapException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            return new KingdeeApiException(
                String.format("API请求失败: %s - %s", ex.getStatusCode(), ex.getResponseBodyAsString()),
                throwable
            );
        }
        return new KingdeeApiException("API请求异常", throwable);
    }
    
    /**
     * 查询数据
     */
    public Mono<KingdeeResponse> queryData(String formId, Map<String, Object> queryParams) {
        KingdeeRequest request = KingdeeRequest.builder()
                .formId(formId + ".ExecuteBillQuery")
                .data(queryParams)
                .build();
        
        return executeRequest(request);
    }
    
    /**
     * 保存数据
     */
    public Mono<KingdeeResponse> saveData(String formId, Map<String, Object> data) {
        KingdeeRequest request = KingdeeRequest.builder()
                .formId(formId + ".Save")
                .data(data)
                .build();
        
        return executeRequest(request);
    }
    
    /**
     * 删除数据
     */
    public Mono<KingdeeResponse> deleteData(String formId, Map<String, Object> deleteParams) {
        KingdeeRequest request = KingdeeRequest.builder()
                .formId(formId + ".Delete")
                .data(deleteParams)
                .build();
        
        return executeRequest(request);
    }
    
    /**
     * 获取表单元数据
     */
    public Mono<KingdeeResponse> getFormMetadata(String formId) {
        KingdeeRequest request = KingdeeRequest.builder()
                .formId(formId + ".GetFormMetaData")
                .data(Map.of("FormId", formId))
                .build();
        
        return executeRequest(request);
    }
}
