package com.enterprise.bi.visualization.repository;

import com.enterprise.bi.visualization.entity.Dashboard;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 仪表板仓库接口
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Repository
public interface DashboardRepository extends JpaRepository<Dashboard, Long> {
    
    /**
     * 根据类型查找仪表板
     */
    List<Dashboard> findByType(String type);
    
    /**
     * 根据创建者查找仪表板
     */
    List<Dashboard> findByCreatedBy(String createdBy);
    
    /**
     * 根据启用状态查找仪表板
     */
    List<Dashboard> findByIsEnabled(Boolean isEnabled);
    
    /**
     * 根据公开状态查找仪表板
     */
    List<Dashboard> findByIsPublic(Boolean isPublic);
    
    /**
     * 根据启用状态统计仪表板数量
     */
    long countByIsEnabled(Boolean isEnabled);
    
    /**
     * 根据公开状态统计仪表板数量
     */
    long countByIsPublic(Boolean isPublic);
    
    /**
     * 搜索仪表板（名称、描述）
     */
    Page<Dashboard> findByNameContainingOrDescriptionContaining(
            String name, String description, Pageable pageable);
    
    /**
     * 获取公开且启用的仪表板，按排序顺序
     */
    List<Dashboard> findByIsPublicTrueAndIsEnabledTrueOrderBySortOrder();
    
    /**
     * 根据创建时间范围查找仪表板
     */
    List<Dashboard> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据最后访问时间范围查找仪表板
     */
    List<Dashboard> findByLastViewedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找最近访问的仪表板
     */
    @Query("SELECT d FROM Dashboard d WHERE d.lastViewedAt >= :since ORDER BY d.lastViewedAt DESC")
    List<Dashboard> findRecentlyViewedDashboards(@Param("since") LocalDateTime since);
    
    /**
     * 查找长时间未访问的仪表板
     */
    @Query("SELECT d FROM Dashboard d WHERE d.lastViewedAt < :before OR d.lastViewedAt IS NULL")
    List<Dashboard> findInactiveDashboards(@Param("before") LocalDateTime before);
    
    /**
     * 根据类型和启用状态查找仪表板
     */
    List<Dashboard> findByTypeAndIsEnabled(String type, Boolean isEnabled);
    
    /**
     * 统计各类型仪表板数量
     */
    @Query("SELECT d.type, COUNT(d) FROM Dashboard d GROUP BY d.type")
    List<Object[]> countDashboardsByType();
    
    /**
     * 查找今日创建的仪表板
     */
    @Query("SELECT d FROM Dashboard d WHERE DATE(d.createdAt) = CURRENT_DATE")
    List<Dashboard> findTodayCreatedDashboards();
    
    /**
     * 查找今日访问的仪表板
     */
    @Query("SELECT d FROM Dashboard d WHERE DATE(d.lastViewedAt) = CURRENT_DATE")
    List<Dashboard> findTodayViewedDashboards();
    
    /**
     * 查找访问次数最多的仪表板
     */
    @Query("SELECT d FROM Dashboard d ORDER BY d.viewCount DESC")
    List<Dashboard> findMostViewedDashboards(Pageable pageable);
    
    /**
     * 根据排序顺序查找启用的仪表板
     */
    List<Dashboard> findByIsEnabledTrueOrderBySortOrder();
    
    /**
     * 查找指定用户创建的启用仪表板
     */
    List<Dashboard> findByCreatedByAndIsEnabledTrue(String createdBy);
}
