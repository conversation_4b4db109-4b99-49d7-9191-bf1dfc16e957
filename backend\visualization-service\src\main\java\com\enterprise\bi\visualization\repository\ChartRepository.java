package com.enterprise.bi.visualization.repository;

import com.enterprise.bi.visualization.entity.Chart;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 图表仓库接口
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Repository
public interface ChartRepository extends JpaRepository<Chart, Long> {
    
    /**
     * 根据类型查找图表
     */
    List<Chart> findByType(String type);
    
    /**
     * 根据创建者查找图表
     */
    List<Chart> findByCreatedBy(String createdBy);
    
    /**
     * 根据启用状态查找图表
     */
    List<Chart> findByIsEnabled(Boolean isEnabled);
    
    /**
     * 根据实时状态查找图表
     */
    List<Chart> findByIsRealtime(Boolean isRealtime);
    
    /**
     * 根据启用状态统计图表数量
     */
    long countByIsEnabled(Boolean isEnabled);
    
    /**
     * 根据实时状态统计图表数量
     */
    long countByIsRealtime(Boolean isRealtime);
    
    /**
     * 搜索图表（名称、描述）
     */
    Page<Chart> findByNameContainingOrDescriptionContaining(
            String name, String description, Pageable pageable);
    
    /**
     * 根据数据源ID查找图表
     */
    List<Chart> findByDataSourceId(Long dataSourceId);
    
    /**
     * 根据创建时间范围查找图表
     */
    List<Chart> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据最后执行时间范围查找图表
     */
    List<Chart> findByLastExecutedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找最近执行的图表
     */
    @Query("SELECT c FROM Chart c WHERE c.lastExecutedAt >= :since ORDER BY c.lastExecutedAt DESC")
    List<Chart> findRecentlyExecutedCharts(@Param("since") LocalDateTime since);
    
    /**
     * 查找长时间未执行的图表
     */
    @Query("SELECT c FROM Chart c WHERE c.lastExecutedAt < :before OR c.lastExecutedAt IS NULL")
    List<Chart> findInactiveCharts(@Param("before") LocalDateTime before);
    
    /**
     * 根据类型和启用状态查找图表
     */
    List<Chart> findByTypeAndIsEnabled(String type, Boolean isEnabled);
    
    /**
     * 统计各类型图表数量
     */
    @Query("SELECT c.type, COUNT(c) FROM Chart c GROUP BY c.type")
    List<Object[]> countChartsByType();
    
    /**
     * 查找今日创建的图表
     */
    @Query("SELECT c FROM Chart c WHERE DATE(c.createdAt) = CURRENT_DATE")
    List<Chart> findTodayCreatedCharts();
    
    /**
     * 查找今日执行的图表
     */
    @Query("SELECT c FROM Chart c WHERE DATE(c.lastExecutedAt) = CURRENT_DATE")
    List<Chart> findTodayExecutedCharts();
    
    /**
     * 查找执行次数最多的图表
     */
    @Query("SELECT c FROM Chart c ORDER BY c.executionCount DESC")
    List<Chart> findMostExecutedCharts(Pageable pageable);
    
    /**
     * 查找启用的实时图表
     */
    List<Chart> findByIsEnabledTrueAndIsRealtimeTrue();
    
    /**
     * 根据刷新间隔查找图表
     */
    List<Chart> findByRefreshInterval(Integer refreshInterval);
    
    /**
     * 查找需要刷新的图表
     */
    @Query("SELECT c FROM Chart c WHERE c.isEnabled = true AND c.isRealtime = false " +
           "AND (c.lastExecutedAt IS NULL OR c.lastExecutedAt < :threshold)")
    List<Chart> findChartsNeedingRefresh(@Param("threshold") LocalDateTime threshold);
}
