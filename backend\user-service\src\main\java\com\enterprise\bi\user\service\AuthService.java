package com.enterprise.bi.user.service;

import com.enterprise.bi.user.entity.User;
import com.enterprise.bi.user.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 认证服务类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {
    
    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider jwtTokenProvider;
    private final UserService userService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String REFRESH_TOKEN_PREFIX = "refresh_token:";
    private static final String BLACKLIST_TOKEN_PREFIX = "blacklist_token:";
    
    /**
     * 用户登录
     */
    public LoginResult login(String username, String password) {
        try {
            // 执行认证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, password)
            );
            
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            
            // 生成令牌对
            JwtTokenProvider.TokenPair tokenPair = jwtTokenProvider.generateTokenPair(userDetails);
            
            // 将刷新令牌存储到Redis
            storeRefreshToken(username, tokenPair.getRefreshToken());
            
            // 更新最后登录时间
            userService.updateLastLoginTime(username);
            
            // 获取用户信息
            User user = userService.getUserByUsername(username).orElse(null);
            
            log.info("用户 {} 登录成功", username);
            
            return new LoginResult(true, "登录成功", tokenPair.getAccessToken(), 
                    tokenPair.getRefreshToken(), user);
            
        } catch (AuthenticationException e) {
            log.warn("用户 {} 登录失败: {}", username, e.getMessage());
            return new LoginResult(false, "用户名或密码错误", null, null, null);
        }
    }
    
    /**
     * 刷新令牌
     */
    public RefreshResult refreshToken(String refreshToken) {
        try {
            // 验证刷新令牌
            if (!jwtTokenProvider.validateToken(refreshToken)) {
                return new RefreshResult(false, "刷新令牌无效", null);
            }
            
            String username = jwtTokenProvider.getUsernameFromToken(refreshToken);
            
            // 检查刷新令牌是否存在于Redis中
            String storedToken = (String) redisTemplate.opsForValue()
                    .get(REFRESH_TOKEN_PREFIX + username);
            
            if (storedToken == null || !storedToken.equals(refreshToken)) {
                return new RefreshResult(false, "刷新令牌已失效", null);
            }
            
            // 加载用户信息
            UserDetails userDetails = userService.loadUserByUsername(username);
            
            // 生成新的访问令牌
            String newAccessToken = jwtTokenProvider.generateAccessToken(userDetails);
            
            log.info("用户 {} 刷新令牌成功", username);
            
            return new RefreshResult(true, "令牌刷新成功", newAccessToken);
            
        } catch (Exception e) {
            log.error("刷新令牌失败", e);
            return new RefreshResult(false, "刷新令牌失败", null);
        }
    }
    
    /**
     * 用户登出
     */
    public void logout(String username, String accessToken, String refreshToken) {
        try {
            // 将访问令牌加入黑名单
            if (accessToken != null) {
                blacklistToken(accessToken);
            }
            
            // 删除刷新令牌
            if (refreshToken != null) {
                removeRefreshToken(username);
            }
            
            log.info("用户 {} 登出成功", username);
            
        } catch (Exception e) {
            log.error("用户 {} 登出失败", username, e);
        }
    }
    
    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        // 检查令牌是否在黑名单中
        if (isTokenBlacklisted(token)) {
            return false;
        }
        
        return jwtTokenProvider.validateToken(token);
    }
    
    /**
     * 从令牌获取用户名
     */
    public String getUsernameFromToken(String token) {
        return jwtTokenProvider.getUsernameFromToken(token);
    }
    
    /**
     * 存储刷新令牌到Redis
     */
    private void storeRefreshToken(String username, String refreshToken) {
        try {
            // 设置过期时间为7天
            redisTemplate.opsForValue().set(
                    REFRESH_TOKEN_PREFIX + username, 
                    refreshToken, 
                    7, 
                    TimeUnit.DAYS
            );
        } catch (Exception e) {
            log.error("存储刷新令牌失败", e);
        }
    }
    
    /**
     * 删除刷新令牌
     */
    private void removeRefreshToken(String username) {
        try {
            redisTemplate.delete(REFRESH_TOKEN_PREFIX + username);
        } catch (Exception e) {
            log.error("删除刷新令牌失败", e);
        }
    }
    
    /**
     * 将令牌加入黑名单
     */
    private void blacklistToken(String token) {
        try {
            // 获取令牌过期时间
            long expiration = jwtTokenProvider.getExpirationDateFromToken(token).getTime();
            long currentTime = System.currentTimeMillis();
            
            if (expiration > currentTime) {
                long ttl = (expiration - currentTime) / 1000;
                redisTemplate.opsForValue().set(
                        BLACKLIST_TOKEN_PREFIX + token, 
                        "blacklisted", 
                        ttl, 
                        TimeUnit.SECONDS
                );
            }
        } catch (Exception e) {
            log.error("将令牌加入黑名单失败", e);
        }
    }
    
    /**
     * 检查令牌是否在黑名单中
     */
    private boolean isTokenBlacklisted(String token) {
        try {
            return redisTemplate.hasKey(BLACKLIST_TOKEN_PREFIX + token);
        } catch (Exception e) {
            log.error("检查令牌黑名单状态失败", e);
            return false;
        }
    }
    
    /**
     * 登录结果类
     */
    public static class LoginResult {
        private final boolean success;
        private final String message;
        private final String accessToken;
        private final String refreshToken;
        private final User user;
        
        public LoginResult(boolean success, String message, String accessToken, 
                          String refreshToken, User user) {
            this.success = success;
            this.message = message;
            this.accessToken = accessToken;
            this.refreshToken = refreshToken;
            this.user = user;
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getAccessToken() { return accessToken; }
        public String getRefreshToken() { return refreshToken; }
        public User getUser() { return user; }
    }
    
    /**
     * 刷新结果类
     */
    public static class RefreshResult {
        private final boolean success;
        private final String message;
        private final String accessToken;
        
        public RefreshResult(boolean success, String message, String accessToken) {
            this.success = success;
            this.message = message;
            this.accessToken = accessToken;
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getAccessToken() { return accessToken; }
    }
}
