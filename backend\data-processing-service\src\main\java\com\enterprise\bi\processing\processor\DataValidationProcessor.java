package com.enterprise.bi.processing.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数据验证处理器
 * 负责数据质量检查、格式验证、业务规则验证等操作
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Component
public class DataValidationProcessor implements DataProcessor {
    
    @Override
    public Object process(Object data, Map<String, Object> config) {
        log.debug("开始执行数据验证处理");
        
        if (!validateInput(data)) {
            throw new IllegalArgumentException("输入数据格式不正确");
        }
        
        if (data instanceof List) {
            return processListData((List<?>) data, config);
        } else if (data instanceof Map) {
            return processMapData((Map<?, ?>) data, config);
        }
        
        return data;
    }
    
    /**
     * 处理列表数据
     */
    @SuppressWarnings("unchecked")
    private ValidationResult processListData(List<?> dataList, Map<String, Object> config) {
        List<Map<String, Object>> validRecords = new ArrayList<>();
        List<ValidationError> errors = new ArrayList<>();
        
        for (int i = 0; i < dataList.size(); i++) {
            Object item = dataList.get(i);
            if (item instanceof Map) {
                ValidationResult itemResult = validateMapData((Map<String, Object>) item, config, i);
                if (itemResult.isValid()) {
                    validRecords.add((Map<String, Object>) item);
                } else {
                    errors.addAll(itemResult.getErrors());
                }
            }
        }
        
        ValidationResult result = new ValidationResult();
        result.setValid(errors.isEmpty());
        result.setData(validRecords);
        result.setErrors(errors);
        result.setTotalRecords(dataList.size());
        result.setValidRecords(validRecords.size());
        result.setInvalidRecords(errors.size());
        
        log.debug("数据验证完成，总记录数: {}, 有效记录数: {}, 无效记录数: {}", 
                dataList.size(), validRecords.size(), errors.size());
        
        return result;
    }
    
    /**
     * 处理Map数据
     */
    @SuppressWarnings("unchecked")
    private ValidationResult processMapData(Map<?, ?> dataMap, Map<String, Object> config) {
        return validateMapData((Map<String, Object>) dataMap, config, 0);
    }
    
    /**
     * 验证Map数据
     */
    @SuppressWarnings("unchecked")
    private ValidationResult validateMapData(Map<String, Object> dataMap, Map<String, Object> config, int recordIndex) {
        List<ValidationError> errors = new ArrayList<>();
        
        // 获取验证规则
        Map<String, Map<String, Object>> fieldRules = (Map<String, Map<String, Object>>) config.get("fieldRules");
        List<Map<String, Object>> businessRules = (List<Map<String, Object>>) config.get("businessRules");
        
        // 字段级验证
        if (fieldRules != null) {
            for (Map.Entry<String, Map<String, Object>> entry : fieldRules.entrySet()) {
                String fieldName = entry.getKey();
                Map<String, Object> rules = entry.getValue();
                Object fieldValue = dataMap.get(fieldName);
                
                List<ValidationError> fieldErrors = validateField(fieldName, fieldValue, rules, recordIndex);
                errors.addAll(fieldErrors);
            }
        }
        
        // 业务规则验证
        if (businessRules != null) {
            for (Map<String, Object> rule : businessRules) {
                ValidationError businessError = validateBusinessRule(dataMap, rule, recordIndex);
                if (businessError != null) {
                    errors.add(businessError);
                }
            }
        }
        
        ValidationResult result = new ValidationResult();
        result.setValid(errors.isEmpty());
        result.setData(dataMap);
        result.setErrors(errors);
        
        return result;
    }
    
    /**
     * 验证字段
     */
    private List<ValidationError> validateField(String fieldName, Object fieldValue, Map<String, Object> rules, int recordIndex) {
        List<ValidationError> errors = new ArrayList<>();
        
        // 必填验证
        if (getBooleanConfig(rules, "required", false)) {
            if (fieldValue == null || (fieldValue instanceof String && ((String) fieldValue).trim().isEmpty())) {
                errors.add(new ValidationError(recordIndex, fieldName, "REQUIRED", "字段不能为空", fieldValue));
            }
        }
        
        if (fieldValue == null) {
            return errors;
        }
        
        // 数据类型验证
        String expectedType = getStringConfig(rules, "type", "");
        if (!expectedType.isEmpty() && !validateDataType(fieldValue, expectedType)) {
            errors.add(new ValidationError(recordIndex, fieldName, "TYPE_MISMATCH", 
                    "数据类型不匹配，期望: " + expectedType, fieldValue));
        }
        
        // 长度验证
        if (fieldValue instanceof String) {
            String strValue = (String) fieldValue;
            Integer minLength = getIntegerConfig(rules, "minLength", null);
            Integer maxLength = getIntegerConfig(rules, "maxLength", null);
            
            if (minLength != null && strValue.length() < minLength) {
                errors.add(new ValidationError(recordIndex, fieldName, "MIN_LENGTH", 
                        "字符串长度不能少于 " + minLength, fieldValue));
            }
            
            if (maxLength != null && strValue.length() > maxLength) {
                errors.add(new ValidationError(recordIndex, fieldName, "MAX_LENGTH", 
                        "字符串长度不能超过 " + maxLength, fieldValue));
            }
        }
        
        // 数值范围验证
        if (fieldValue instanceof Number) {
            Number numValue = (Number) fieldValue;
            Double minValue = getDoubleConfig(rules, "minValue", null);
            Double maxValue = getDoubleConfig(rules, "maxValue", null);
            
            if (minValue != null && numValue.doubleValue() < minValue) {
                errors.add(new ValidationError(recordIndex, fieldName, "MIN_VALUE", 
                        "数值不能小于 " + minValue, fieldValue));
            }
            
            if (maxValue != null && numValue.doubleValue() > maxValue) {
                errors.add(new ValidationError(recordIndex, fieldName, "MAX_VALUE", 
                        "数值不能大于 " + maxValue, fieldValue));
            }
        }
        
        // 正则表达式验证
        String pattern = getStringConfig(rules, "pattern", "");
        if (!pattern.isEmpty() && fieldValue instanceof String) {
            if (!Pattern.matches(pattern, (String) fieldValue)) {
                errors.add(new ValidationError(recordIndex, fieldName, "PATTERN_MISMATCH", 
                        "格式不匹配，要求格式: " + pattern, fieldValue));
            }
        }
        
        // 枚举值验证
        @SuppressWarnings("unchecked")
        List<Object> allowedValues = (List<Object>) rules.get("allowedValues");
        if (allowedValues != null && !allowedValues.contains(fieldValue)) {
            errors.add(new ValidationError(recordIndex, fieldName, "INVALID_VALUE", 
                    "值不在允许范围内: " + allowedValues, fieldValue));
        }
        
        return errors;
    }
    
    /**
     * 验证业务规则
     */
    private ValidationError validateBusinessRule(Map<String, Object> dataMap, Map<String, Object> rule, int recordIndex) {
        String ruleType = getStringConfig(rule, "type", "");
        String ruleExpression = getStringConfig(rule, "expression", "");
        String errorMessage = getStringConfig(rule, "message", "业务规则验证失败");
        
        switch (ruleType.toUpperCase()) {
            case "CONDITIONAL":
                return validateConditionalRule(dataMap, ruleExpression, errorMessage, recordIndex);
            case "CROSS_FIELD":
                return validateCrossFieldRule(dataMap, rule, errorMessage, recordIndex);
            case "CUSTOM":
                return validateCustomRule(dataMap, ruleExpression, errorMessage, recordIndex);
            default:
                log.warn("不支持的业务规则类型: {}", ruleType);
                return null;
        }
    }
    
    /**
     * 验证条件规则
     */
    private ValidationError validateConditionalRule(Map<String, Object> dataMap, String expression, String errorMessage, int recordIndex) {
        // 简单的条件表达式验证实现
        // 实际项目中可以使用更强大的表达式引擎
        try {
            boolean result = evaluateCondition(dataMap, expression);
            if (!result) {
                return new ValidationError(recordIndex, "BUSINESS_RULE", "CONDITIONAL", errorMessage, expression);
            }
        } catch (Exception e) {
            log.warn("条件规则验证失败: {}", expression, e);
            return new ValidationError(recordIndex, "BUSINESS_RULE", "CONDITIONAL", "条件规则验证异常: " + e.getMessage(), expression);
        }
        return null;
    }
    
    /**
     * 验证跨字段规则
     */
    private ValidationError validateCrossFieldRule(Map<String, Object> dataMap, Map<String, Object> rule, String errorMessage, int recordIndex) {
        String field1 = getStringConfig(rule, "field1", "");
        String field2 = getStringConfig(rule, "field2", "");
        String operator = getStringConfig(rule, "operator", "");
        
        if (field1.isEmpty() || field2.isEmpty() || operator.isEmpty()) {
            return null;
        }
        
        Object value1 = dataMap.get(field1);
        Object value2 = dataMap.get(field2);
        
        boolean result = compareValues(value1, value2, operator);
        if (!result) {
            return new ValidationError(recordIndex, "CROSS_FIELD", "COMPARISON", errorMessage, 
                    String.format("%s %s %s", field1, operator, field2));
        }
        
        return null;
    }
    
    /**
     * 验证自定义规则
     */
    private ValidationError validateCustomRule(Map<String, Object> dataMap, String expression, String errorMessage, int recordIndex) {
        // 这里可以实现自定义规则的验证逻辑
        // 例如调用外部服务、执行复杂计算等
        return null;
    }
    
    /**
     * 评估条件表达式
     */
    private boolean evaluateCondition(Map<String, Object> dataMap, String expression) {
        // 简单的条件表达式求值实现
        // 支持基本的比较操作
        
        // 示例: "age > 18"
        if (expression.contains(">")) {
            String[] parts = expression.split(">");
            String field = parts[0].trim();
            double threshold = Double.parseDouble(parts[1].trim());
            Object value = dataMap.get(field);
            return value instanceof Number && ((Number) value).doubleValue() > threshold;
        }
        
        // 示例: "status == 'ACTIVE'"
        if (expression.contains("==")) {
            String[] parts = expression.split("==");
            String field = parts[0].trim();
            String expectedValue = parts[1].trim().replace("'", "").replace("\"", "");
            Object value = dataMap.get(field);
            return Objects.equals(String.valueOf(value), expectedValue);
        }
        
        return true;
    }
    
    /**
     * 比较两个值
     */
    private boolean compareValues(Object value1, Object value2, String operator) {
        if (value1 == null || value2 == null) {
            return "==".equals(operator) ? Objects.equals(value1, value2) : false;
        }
        
        switch (operator) {
            case "==":
                return Objects.equals(value1, value2);
            case "!=":
                return !Objects.equals(value1, value2);
            case ">":
                return compareNumbers(value1, value2) > 0;
            case ">=":
                return compareNumbers(value1, value2) >= 0;
            case "<":
                return compareNumbers(value1, value2) < 0;
            case "<=":
                return compareNumbers(value1, value2) <= 0;
            default:
                return false;
        }
    }
    
    /**
     * 比较数值
     */
    private int compareNumbers(Object value1, Object value2) {
        if (value1 instanceof Number && value2 instanceof Number) {
            return Double.compare(((Number) value1).doubleValue(), ((Number) value2).doubleValue());
        }
        return 0;
    }
    
    /**
     * 验证数据类型
     */
    private boolean validateDataType(Object value, String expectedType) {
        switch (expectedType.toUpperCase()) {
            case "STRING":
                return value instanceof String;
            case "INTEGER":
                return value instanceof Integer;
            case "LONG":
                return value instanceof Long;
            case "DOUBLE":
                return value instanceof Double;
            case "BOOLEAN":
                return value instanceof Boolean;
            case "NUMBER":
                return value instanceof Number;
            default:
                return true;
        }
    }
    
    // 配置获取方法
    private boolean getBooleanConfig(Map<String, Object> config, String key, boolean defaultValue) {
        Object value = config.get(key);
        return value instanceof Boolean ? (Boolean) value : defaultValue;
    }
    
    private String getStringConfig(Map<String, Object> config, String key, String defaultValue) {
        Object value = config.get(key);
        return value instanceof String ? (String) value : defaultValue;
    }
    
    private Integer getIntegerConfig(Map<String, Object> config, String key, Integer defaultValue) {
        Object value = config.get(key);
        return value instanceof Integer ? (Integer) value : defaultValue;
    }
    
    private Double getDoubleConfig(Map<String, Object> config, String key, Double defaultValue) {
        Object value = config.get(key);
        return value instanceof Number ? ((Number) value).doubleValue() : defaultValue;
    }
    
    @Override
    public String getProcessorName() {
        return "DataValidationProcessor";
    }
    
    @Override
    public String getProcessorDescription() {
        return "数据验证处理器，负责数据质量检查、格式验证、业务规则验证等操作";
    }
    
    @Override
    public boolean validateInput(Object data) {
        return data instanceof List || data instanceof Map;
    }
    
    @Override
    public Class<?>[] getSupportedDataTypes() {
        return new Class<?>[]{List.class, Map.class};
    }
    
    // 验证结果类
    public static class ValidationResult {
        private boolean valid;
        private Object data;
        private List<ValidationError> errors;
        private int totalRecords;
        private int validRecords;
        private int invalidRecords;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        public Object getData() { return data; }
        public void setData(Object data) { this.data = data; }
        public List<ValidationError> getErrors() { return errors; }
        public void setErrors(List<ValidationError> errors) { this.errors = errors; }
        public int getTotalRecords() { return totalRecords; }
        public void setTotalRecords(int totalRecords) { this.totalRecords = totalRecords; }
        public int getValidRecords() { return validRecords; }
        public void setValidRecords(int validRecords) { this.validRecords = validRecords; }
        public int getInvalidRecords() { return invalidRecords; }
        public void setInvalidRecords(int invalidRecords) { this.invalidRecords = invalidRecords; }
    }
    
    // 验证错误类
    public static class ValidationError {
        private int recordIndex;
        private String fieldName;
        private String errorType;
        private String message;
        private Object value;
        
        public ValidationError(int recordIndex, String fieldName, String errorType, String message, Object value) {
            this.recordIndex = recordIndex;
            this.fieldName = fieldName;
            this.errorType = errorType;
            this.message = message;
            this.value = value;
        }
        
        // Getters and Setters
        public int getRecordIndex() { return recordIndex; }
        public String getFieldName() { return fieldName; }
        public String getErrorType() { return errorType; }
        public String getMessage() { return message; }
        public Object getValue() { return value; }
    }
}
