package com.enterprise.bi.integration.kingdee.service;

import com.enterprise.bi.integration.kingdee.client.KingdeeApiClient;
import com.enterprise.bi.integration.kingdee.config.KingdeeConfig;
import com.enterprise.bi.integration.kingdee.dto.KingdeeResponse;
import com.enterprise.bi.integration.kingdee.entity.SyncTask;
import com.enterprise.bi.integration.kingdee.entity.SyncRecord;
import com.enterprise.bi.integration.kingdee.repository.SyncTaskRepository;
import com.enterprise.bi.integration.kingdee.repository.SyncRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 金蝶数据同步服务
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KingdeeDataSyncService {
    
    private final KingdeeApiClient kingdeeApiClient;
    private final KingdeeConfig kingdeeConfig;
    private final SyncTaskRepository syncTaskRepository;
    private final SyncRecordRepository syncRecordRepository;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    /**
     * 定时同步任务
     */
    @Scheduled(fixedDelayString = "${kingdee.sync-interval:300}000")
    public void scheduledSync() {
        log.info("开始执行定时同步任务...");
        
        List<SyncTask> activeTasks = syncTaskRepository.findByStatusAndEnabled("ACTIVE", true);
        
        for (SyncTask task : activeTasks) {
            try {
                executeSync(task).subscribe(
                    result -> log.info("同步任务 {} 执行成功", task.getName()),
                    error -> log.error("同步任务 {} 执行失败", task.getName(), error)
                );
            } catch (Exception e) {
                log.error("同步任务 {} 执行异常", task.getName(), e);
            }
        }
    }
    
    /**
     * 执行同步任务
     */
    @Async
    public CompletableFuture<Void> executeSyncAsync(Long taskId) {
        return CompletableFuture.runAsync(() -> {
            SyncTask task = syncTaskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("同步任务不存在: " + taskId));
            
            executeSync(task).block();
        });
    }
    
    /**
     * 执行同步
     */
    public Mono<Void> executeSync(SyncTask task) {
        log.info("开始执行同步任务: {}", task.getName());
        
        SyncRecord record = createSyncRecord(task);
        
        return performSync(task, record)
                .doOnSuccess(result -> {
                    record.setStatus("SUCCESS");
                    record.setEndTime(LocalDateTime.now());
                    record.setMessage("同步成功");
                    saveSyncRecord(record);
                    
                    // 发送同步完成事件
                    publishSyncEvent(task, record, "SYNC_COMPLETED");
                })
                .doOnError(error -> {
                    record.setStatus("FAILED");
                    record.setEndTime(LocalDateTime.now());
                    record.setMessage("同步失败: " + error.getMessage());
                    saveSyncRecord(record);
                    
                    // 发送同步失败事件
                    publishSyncEvent(task, record, "SYNC_FAILED");
                })
                .then();
    }
    
    /**
     * 执行具体的同步逻辑
     */
    private Mono<Void> performSync(SyncTask task, SyncRecord record) {
        switch (task.getDataType()) {
            case "SALES_ORDER":
                return syncSalesOrders(task, record);
            case "PURCHASE_ORDER":
                return syncPurchaseOrders(task, record);
            case "INVENTORY":
                return syncInventory(task, record);
            case "CUSTOMER":
                return syncCustomers(task, record);
            case "SUPPLIER":
                return syncSuppliers(task, record);
            default:
                return Mono.error(new IllegalArgumentException("不支持的数据类型: " + task.getDataType()));
        }
    }
    
    /**
     * 同步销售订单
     */
    private Mono<Void> syncSalesOrders(SyncTask task, SyncRecord record) {
        log.info("开始同步销售订单数据...");
        
        Map<String, Object> queryParams = buildQueryParams(task);
        
        return kingdeeApiClient.queryData("SAL_SaleOrder", queryParams)
                .flatMap(response -> processSyncResponse(response, task, record, "sales_orders"))
                .doOnNext(count -> {
                    record.setSyncCount(count);
                    log.info("销售订单同步完成，共同步 {} 条记录", count);
                })
                .then();
    }
    
    /**
     * 同步采购订单
     */
    private Mono<Void> syncPurchaseOrders(SyncTask task, SyncRecord record) {
        log.info("开始同步采购订单数据...");
        
        Map<String, Object> queryParams = buildQueryParams(task);
        
        return kingdeeApiClient.queryData("PUR_PurchaseOrder", queryParams)
                .flatMap(response -> processSyncResponse(response, task, record, "purchase_orders"))
                .doOnNext(count -> {
                    record.setSyncCount(count);
                    log.info("采购订单同步完成，共同步 {} 条记录", count);
                })
                .then();
    }
    
    /**
     * 同步库存数据
     */
    private Mono<Void> syncInventory(SyncTask task, SyncRecord record) {
        log.info("开始同步库存数据...");
        
        Map<String, Object> queryParams = buildQueryParams(task);
        
        return kingdeeApiClient.queryData("STK_Inventory", queryParams)
                .flatMap(response -> processSyncResponse(response, task, record, "inventory"))
                .doOnNext(count -> {
                    record.setSyncCount(count);
                    log.info("库存数据同步完成，共同步 {} 条记录", count);
                })
                .then();
    }
    
    /**
     * 同步客户数据
     */
    private Mono<Void> syncCustomers(SyncTask task, SyncRecord record) {
        log.info("开始同步客户数据...");
        
        Map<String, Object> queryParams = buildQueryParams(task);
        
        return kingdeeApiClient.queryData("BD_Customer", queryParams)
                .flatMap(response -> processSyncResponse(response, task, record, "customers"))
                .doOnNext(count -> {
                    record.setSyncCount(count);
                    log.info("客户数据同步完成，共同步 {} 条记录", count);
                })
                .then();
    }
    
    /**
     * 同步供应商数据
     */
    private Mono<Void> syncSuppliers(SyncTask task, SyncRecord record) {
        log.info("开始同步供应商数据...");
        
        Map<String, Object> queryParams = buildQueryParams(task);
        
        return kingdeeApiClient.queryData("BD_Supplier", queryParams)
                .flatMap(response -> processSyncResponse(response, task, record, "suppliers"))
                .doOnNext(count -> {
                    record.setSyncCount(count);
                    log.info("供应商数据同步完成，共同步 {} 条记录", count);
                })
                .then();
    }
    
    /**
     * 构建查询参数
     */
    private Map<String, Object> buildQueryParams(SyncTask task) {
        Map<String, Object> params = Map.of(
            "FormId", getFormIdByDataType(task.getDataType()),
            "FieldKeys", getFieldKeysByDataType(task.getDataType()),
            "FilterString", buildFilterString(task),
            "OrderString", "FModifyDate DESC",
            "TopRowCount", kingdeeConfig.getBatchSize(),
            "StartRow", 0
        );
        
        log.debug("查询参数: {}", params);
        return params;
    }
    
    /**
     * 根据数据类型获取表单ID
     */
    private String getFormIdByDataType(String dataType) {
        switch (dataType) {
            case "SALES_ORDER": return "SAL_SaleOrder";
            case "PURCHASE_ORDER": return "PUR_PurchaseOrder";
            case "INVENTORY": return "STK_Inventory";
            case "CUSTOMER": return "BD_Customer";
            case "SUPPLIER": return "BD_Supplier";
            default: throw new IllegalArgumentException("不支持的数据类型: " + dataType);
        }
    }
    
    /**
     * 根据数据类型获取字段键
     */
    private String getFieldKeysByDataType(String dataType) {
        switch (dataType) {
            case "SALES_ORDER": 
                return "FBillNo,FDate,FCustId,FAmount,FBillStatus,FModifyDate";
            case "PURCHASE_ORDER": 
                return "FBillNo,FDate,FSupplierId,FAmount,FBillStatus,FModifyDate";
            case "INVENTORY": 
                return "FMaterialId,FStockId,FQty,FAmount,FModifyDate";
            case "CUSTOMER": 
                return "FNumber,FName,FShortName,FContact,FPhone,FModifyDate";
            case "SUPPLIER": 
                return "FNumber,FName,FShortName,FContact,FPhone,FModifyDate";
            default: 
                return "*";
        }
    }
    
    /**
     * 构建过滤条件
     */
    private String buildFilterString(SyncTask task) {
        if (task.getLastSyncTime() != null && kingdeeConfig.isIncrementalSyncEnabled()) {
            return String.format("FModifyDate >= '%s'", task.getLastSyncTime());
        }
        return "";
    }
    
    /**
     * 处理同步响应
     */
    private Mono<Integer> processSyncResponse(KingdeeResponse response, SyncTask task, 
                                            SyncRecord record, String topic) {
        if (!response.isSuccess()) {
            return Mono.error(new RuntimeException("API调用失败: " + response.getMessage()));
        }
        
        Object data = response.getData();
        if (data == null) {
            return Mono.just(0);
        }
        
        // 发送数据到Kafka进行后续处理
        kafkaTemplate.send(topic, data);
        
        // 这里应该解析实际的数据条数，暂时返回1
        return Mono.just(1);
    }
    
    /**
     * 创建同步记录
     */
    private SyncRecord createSyncRecord(SyncTask task) {
        SyncRecord record = new SyncRecord();
        record.setTaskId(task.getId());
        record.setTaskName(task.getName());
        record.setDataType(task.getDataType());
        record.setStatus("RUNNING");
        record.setStartTime(LocalDateTime.now());
        record.setSyncCount(0);
        
        return syncRecordRepository.save(record);
    }
    
    /**
     * 保存同步记录
     */
    @Transactional
    public void saveSyncRecord(SyncRecord record) {
        syncRecordRepository.save(record);
        
        // 更新任务的最后同步时间
        if ("SUCCESS".equals(record.getStatus())) {
            SyncTask task = syncTaskRepository.findById(record.getTaskId()).orElse(null);
            if (task != null) {
                task.setLastSyncTime(record.getEndTime());
                syncTaskRepository.save(task);
            }
        }
    }
    
    /**
     * 发布同步事件
     */
    private void publishSyncEvent(SyncTask task, SyncRecord record, String eventType) {
        Map<String, Object> event = Map.of(
            "eventType", eventType,
            "taskId", task.getId(),
            "taskName", task.getName(),
            "dataType", task.getDataType(),
            "syncCount", record.getSyncCount(),
            "status", record.getStatus(),
            "message", record.getMessage(),
            "timestamp", LocalDateTime.now()
        );
        
        kafkaTemplate.send("sync_events", event);
    }
}
