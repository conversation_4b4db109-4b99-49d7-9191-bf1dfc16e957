package com.enterprise.bi.processing.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据处理结果模型
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessingResult {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 处理状态
     */
    private String status;
    
    /**
     * 处理后的数据
     */
    private Object data;
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;
    
    /**
     * 处理耗时（毫秒）
     */
    public Long getDuration() {
        if (startTime != null && endTime != null) {
            return endTime - startTime;
        }
        return null;
    }
    
    /**
     * 处理消息
     */
    private String message;
    
    /**
     * 错误详情
     */
    private String errorDetail;
    
    /**
     * 处理统计信息
     */
    private ProcessingStatistics statistics;
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 处理步骤结果
     */
    private Map<String, Object> stepResults;
    
    /**
     * 数据质量评分
     */
    private Double qualityScore;
    
    /**
     * 处理器版本
     */
    private String processorVersion;
    
    /**
     * 处理统计信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingStatistics {
        
        /**
         * 输入记录数
         */
        private Long inputRecordCount;
        
        /**
         * 输出记录数
         */
        private Long outputRecordCount;
        
        /**
         * 错误记录数
         */
        private Long errorRecordCount;
        
        /**
         * 跳过记录数
         */
        private Long skippedRecordCount;
        
        /**
         * 处理速度（记录/秒）
         */
        private Double processingRate;
        
        /**
         * 内存使用量（MB）
         */
        private Double memoryUsageMB;
        
        /**
         * CPU使用率（%）
         */
        private Double cpuUsagePercent;
        
        /**
         * 数据大小（字节）
         */
        private Long dataSizeBytes;
        
        /**
         * 压缩率
         */
        private Double compressionRatio;
    }
}
