import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'
import type { ApiResponse } from '@/types/user'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${userStore.token}`
      }
    }

    // 添加请求ID用于追踪
    config.headers = {
      ...config.headers,
      'X-Request-ID': generateRequestId()
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    // 检查业务状态码
    if (data.code === 200) {
      return response
    }
    
    // 处理业务错误
    handleBusinessError(data)
    return Promise.reject(new Error(data.message || '请求失败'))
  },
  (error) => {
    // 处理HTTP错误
    handleHttpError(error)
    return Promise.reject(error)
  }
)

// 处理业务错误
function handleBusinessError(data: ApiResponse) {
  const { code, message } = data
  
  switch (code) {
    case 401:
      // 未认证，跳转到登录页
      handleUnauthorized()
      break
    case 403:
      // 无权限
      ElMessage.error(message || '无权限访问')
      break
    case 404:
      ElMessage.error(message || '请求的资源不存在')
      break
    case 500:
      ElMessage.error(message || '服务器内部错误')
      break
    default:
      ElMessage.error(message || '请求失败')
  }
}

// 处理HTTP错误
function handleHttpError(error: any) {
  const { response, message } = error
  
  if (response) {
    const { status, data } = response
    
    switch (status) {
      case 401:
        handleUnauthorized()
        break
      case 403:
        ElMessage.error('无权限访问')
        break
      case 404:
        ElMessage.error('请求的接口不存在')
        break
      case 500:
        ElMessage.error('服务器内部错误')
        break
      case 502:
        ElMessage.error('网关错误')
        break
      case 503:
        ElMessage.error('服务不可用')
        break
      case 504:
        ElMessage.error('网关超时')
        break
      default:
        ElMessage.error(data?.message || `请求失败 (${status})`)
    }
  } else if (message.includes('timeout')) {
    ElMessage.error('请求超时，请稍后重试')
  } else if (message.includes('Network Error')) {
    ElMessage.error('网络连接失败，请检查网络')
  } else {
    ElMessage.error('请求失败，请稍后重试')
  }
}

// 处理未认证错误
function handleUnauthorized() {
  const userStore = useUserStore()
  
  ElMessageBox.confirm(
    '登录状态已过期，请重新登录',
    '提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    userStore.logout()
    router.push('/login')
  }).catch(() => {
    // 用户取消
  })
}

// 生成请求ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 请求重试机制
export function createRetryRequest(maxRetries = 3) {
  return async function retryRequest<T>(
    requestFn: () => Promise<T>,
    retries = 0
  ): Promise<T> {
    try {
      return await requestFn()
    } catch (error: any) {
      if (retries < maxRetries && isRetryableError(error)) {
        console.warn(`请求失败，正在重试 (${retries + 1}/${maxRetries})`)
        await delay(1000 * Math.pow(2, retries)) // 指数退避
        return retryRequest(requestFn, retries + 1)
      }
      throw error
    }
  }
}

// 判断是否为可重试的错误
function isRetryableError(error: any): boolean {
  const retryableStatuses = [408, 429, 500, 502, 503, 504]
  return (
    error.code === 'ECONNABORTED' || // 超时
    error.code === 'NETWORK_ERROR' || // 网络错误
    (error.response && retryableStatuses.includes(error.response.status))
  )
}

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export default request
