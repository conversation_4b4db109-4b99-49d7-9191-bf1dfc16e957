<template>
  <div class="system-container">
    <div class="page-header">
      <h1 class="page-title">系统管理</h1>
      <p class="page-description">系统配置、用户管理和运维监控</p>
    </div>

    <el-tabs v-model="activeTab" class="system-tabs">
      <el-tab-pane label="用户管理" name="users">
        <div class="tab-content">
          <div class="content-header">
            <el-button type="primary" @click="showCreateUser = true">
              <el-icon><Plus /></el-icon>
              添加用户
            </el-button>
          </div>
          
          <el-table :data="users" style="width: 100%">
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="realName" label="真实姓名" />
            <el-table-column prop="email" label="邮箱" />
            <el-table-column prop="department" label="部门" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                  {{ row.status === 'active' ? '正常' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="lastLoginAt" label="最后登录">
              <template #default="{ row }">
                {{ formatTime(row.lastLoginAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" @click="editUser(row.id)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteUser(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="系统配置" name="config">
        <div class="tab-content">
          <el-form :model="systemConfig" label-width="150px">
            <el-form-item label="系统名称">
              <el-input v-model="systemConfig.systemName" />
            </el-form-item>
            <el-form-item label="系统描述">
              <el-input v-model="systemConfig.systemDescription" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="数据同步间隔">
              <el-select v-model="systemConfig.syncInterval">
                <el-option label="5分钟" :value="5" />
                <el-option label="10分钟" :value="10" />
                <el-option label="30分钟" :value="30" />
                <el-option label="1小时" :value="60" />
              </el-select>
            </el-form-item>
            <el-form-item label="启用邮件通知">
              <el-switch v-model="systemConfig.emailNotification" />
            </el-form-item>
            <el-form-item label="启用短信通知">
              <el-switch v-model="systemConfig.smsNotification" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSystemConfig">保存配置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="系统监控" name="monitor">
        <div class="tab-content">
          <div class="monitor-grid">
            <div class="monitor-card">
              <h3>系统状态</h3>
              <div class="status-item">
                <span class="status-label">CPU使用率:</span>
                <el-progress :percentage="systemStatus.cpu" :color="getProgressColor(systemStatus.cpu)" />
              </div>
              <div class="status-item">
                <span class="status-label">内存使用率:</span>
                <el-progress :percentage="systemStatus.memory" :color="getProgressColor(systemStatus.memory)" />
              </div>
              <div class="status-item">
                <span class="status-label">磁盘使用率:</span>
                <el-progress :percentage="systemStatus.disk" :color="getProgressColor(systemStatus.disk)" />
              </div>
            </div>
            
            <div class="monitor-card">
              <h3>服务状态</h3>
              <div class="service-list">
                <div class="service-item" v-for="service in services" :key="service.name">
                  <span class="service-name">{{ service.name }}</span>
                  <el-tag :type="service.status === 'running' ? 'success' : 'danger'" size="small">
                    {{ service.status === 'running' ? '运行中' : '已停止' }}
                  </el-tag>
                </div>
              </div>
            </div>
            
            <div class="monitor-card">
              <h3>数据库连接</h3>
              <div class="db-status">
                <div class="db-item" v-for="db in databases" :key="db.name">
                  <span class="db-name">{{ db.name }}</span>
                  <span class="db-connections">连接数: {{ db.connections }}</span>
                  <el-tag :type="db.status === 'connected' ? 'success' : 'danger'" size="small">
                    {{ db.status === 'connected' ? '已连接' : '断开' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="操作日志" name="logs">
        <div class="tab-content">
          <div class="log-filters">
            <el-select v-model="logFilter.level" placeholder="日志级别" style="width: 120px">
              <el-option label="全部" value="" />
              <el-option label="INFO" value="info" />
              <el-option label="WARN" value="warn" />
              <el-option label="ERROR" value="error" />
            </el-select>
            <el-date-picker
              v-model="logFilter.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 350px"
            />
            <el-button type="primary" @click="searchLogs">搜索</el-button>
          </div>
          
          <el-table :data="logs" style="width: 100%">
            <el-table-column prop="timestamp" label="时间" width="180">
              <template #default="{ row }">
                {{ formatTime(row.timestamp) }}
              </template>
            </el-table-column>
            <el-table-column prop="level" label="级别" width="80">
              <template #default="{ row }">
                <el-tag :type="getLogLevelType(row.level)" size="small">
                  {{ row.level }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="module" label="模块" width="120" />
            <el-table-column prop="message" label="消息" />
            <el-table-column prop="user" label="用户" width="100" />
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 创建用户对话框 -->
    <el-dialog
      v-model="showCreateUser"
      title="添加用户"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="userForm" :rules="userRules" ref="userFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="userForm.department" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
            <el-option label="只读用户" value="readonly" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateUser = false">取消</el-button>
        <el-button type="primary" @click="createUser">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { formatTime } from '@/utils/date'

// 表单引用
const userFormRef = ref<FormInstance>()

// 状态
const activeTab = ref('users')
const showCreateUser = ref(false)

// 用户列表
const users = ref([
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    department: 'IT部',
    status: 'active',
    lastLoginAt: new Date()
  },
  {
    id: 2,
    username: 'user1',
    realName: '张三',
    email: '<EMAIL>',
    department: '销售部',
    status: 'active',
    lastLoginAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  }
])

// 系统配置
const systemConfig = reactive({
  systemName: '企业数据可视化BI平台',
  systemDescription: '统一的企业数据集成与可视化系统',
  syncInterval: 5,
  emailNotification: true,
  smsNotification: false
})

// 系统状态
const systemStatus = ref({
  cpu: 45,
  memory: 68,
  disk: 32
})

// 服务状态
const services = ref([
  { name: 'API网关', status: 'running' },
  { name: '认证服务', status: 'running' },
  { name: '数据集成服务', status: 'running' },
  { name: '可视化服务', status: 'running' },
  { name: '通知服务', status: 'stopped' }
])

// 数据库状态
const databases = ref([
  { name: 'PostgreSQL', connections: 15, status: 'connected' },
  { name: 'Redis', connections: 8, status: 'connected' },
  { name: 'ClickHouse', connections: 3, status: 'connected' }
])

// 操作日志
const logs = ref([
  {
    timestamp: new Date(),
    level: 'INFO',
    module: '用户管理',
    message: '用户admin登录系统',
    user: 'admin'
  },
  {
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    level: 'WARN',
    module: '数据同步',
    message: '金蝶云星辰连接超时',
    user: 'system'
  },
  {
    timestamp: new Date(Date.now() - 10 * 60 * 1000),
    level: 'ERROR',
    module: '通知服务',
    message: '邮件发送失败',
    user: 'system'
  }
])

// 日志过滤器
const logFilter = reactive({
  level: '',
  dateRange: []
})

// 用户表单
const userForm = reactive({
  username: '',
  realName: '',
  email: '',
  department: '',
  role: ''
})

// 表单验证规则
const userRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 获取日志级别类型
const getLogLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    INFO: 'success',
    WARN: 'warning',
    ERROR: 'danger'
  }
  return typeMap[level] || 'info'
}

// 编辑用户
const editUser = (userId: number) => {
  ElMessage.info(`编辑用户 ${userId}`)
}

// 删除用户
const deleteUser = async (userId: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这个用户吗？', '确认删除', {
      type: 'warning'
    })
    ElMessage.success('用户删除成功')
  } catch (error) {
    // 用户取消
  }
}

// 创建用户
const createUser = async () => {
  if (!userFormRef.value) return
  
  try {
    await userFormRef.value.validate()
    ElMessage.success('用户创建成功')
    showCreateUser.value = false
    
    // 重置表单
    Object.assign(userForm, {
      username: '',
      realName: '',
      email: '',
      department: '',
      role: ''
    })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 保存系统配置
const saveSystemConfig = () => {
  ElMessage.success('系统配置保存成功')
}

// 搜索日志
const searchLogs = () => {
  ElMessage.success('日志搜索完成')
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.system-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 24px;
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
  
  .page-description {
    color: #909399;
    font-size: 14px;
  }
}

.system-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  :deep(.el-tabs__header) {
    margin: 0;
    padding: 0 24px;
    background: #fafafa;
    border-radius: 8px 8px 0 0;
  }
  
  :deep(.el-tabs__content) {
    padding: 0;
  }
}

.tab-content {
  padding: 24px;
}

.content-header {
  margin-bottom: 20px;
}

.monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.monitor-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 20px;
  
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
  }
}

.status-item {
  margin-bottom: 16px;
  
  .status-label {
    display: block;
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
  }
}

.service-list, .db-status {
  .service-item, .db-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .service-name, .db-name {
    font-size: 14px;
    color: #303133;
  }
  
  .db-connections {
    font-size: 12px;
    color: #909399;
  }
}

.log-filters {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  align-items: center;
}

@media (max-width: 768px) {
  .system-container {
    padding: 16px;
  }
  
  .monitor-grid {
    grid-template-columns: 1fr;
  }
  
  .log-filters {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
