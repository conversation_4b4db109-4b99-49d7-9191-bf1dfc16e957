// 用户相关类型定义

export interface User {
  id: string
  username: string
  email: string
  phone?: string
  avatar?: string
  realName: string
  department?: string
  position?: string
  status: 'active' | 'inactive' | 'locked'
  roles?: UserRole[]
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
}

export interface UserRole {
  id: string
  code: string
  name: string
  description?: string
}

export interface UserPermission {
  id: string
  code: string
  name: string
  type: 'menu' | 'button' | 'api'
  resource?: string
  action?: string
}

export interface LoginForm {
  username: string
  password: string
  captcha?: string
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  user: User
  permissions: UserPermission[]
}

export interface ChangePasswordForm {
  oldPassword: string
  newPassword: string
  confirmPassword?: string
}

export interface UserProfile {
  realName: string
  email: string
  phone?: string
  avatar?: string
  department?: string
  position?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页响应类型
export interface PageResponse<T = any> {
  list: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 用户查询参数
export interface UserQueryParams {
  page?: number
  size?: number
  username?: string
  email?: string
  department?: string
  status?: string
  startDate?: string
  endDate?: string
}
