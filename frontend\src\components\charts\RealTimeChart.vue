<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

const chartRef = ref<HTMLElement>()
let chartInstance: ECharts | null = null
let updateTimer: NodeJS.Timeout | null = null

// 实时数据
const realtimeData = {
  categories: [] as string[],
  data: [] as number[]
}

// 初始化数据
const initData = () => {
  const now = new Date()
  for (let i = 29; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 60000) // 每分钟一个数据点
    realtimeData.categories.push(time.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    }))
    realtimeData.data.push(Math.round(Math.random() * 100 + 50))
  }
}

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0]
        return `${param.name}<br/>实时数据: ${param.value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: realtimeData.categories,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#606266',
        interval: 4 // 显示间隔
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 200,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#606266'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f2f5'
        }
      }
    },
    series: [
      {
        name: '实时数据',
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#67C23A',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(103, 194, 58, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(103, 194, 58, 0.05)'
              }
            ]
          }
        },
        data: realtimeData.data
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const updateData = () => {
  if (!chartInstance) return
  
  // 移除第一个数据点
  realtimeData.categories.shift()
  realtimeData.data.shift()
  
  // 添加新的数据点
  const now = new Date()
  realtimeData.categories.push(now.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  }))
  realtimeData.data.push(Math.round(Math.random() * 100 + 50))
  
  // 更新图表
  chartInstance.setOption({
    xAxis: {
      data: realtimeData.categories
    },
    series: [{
      data: realtimeData.data
    }]
  })
}

const startRealTimeUpdate = () => {
  updateTimer = setInterval(updateData, 5000) // 每5秒更新一次
}

const stopRealTimeUpdate = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  initData()
  initChart()
  startRealTimeUpdate()
  window.addEventListener('resize', resizeChart)
})

onUnmounted(() => {
  stopRealTimeUpdate()
  if (chartInstance) {
    chartInstance.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
