package com.enterprise.bi.user.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限实体类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "permissions")
@EqualsAndHashCode(callSuper = false)
public class Permission {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 权限代码
     */
    @Column(name = "code", nullable = false, unique = true, length = 100)
    private String code;
    
    /**
     * 权限名称
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    /**
     * 权限描述
     */
    @Column(name = "description", length = 500)
    private String description;
    
    /**
     * 权限类型：MENU, BUTTON, API, DATA
     */
    @Column(name = "type", nullable = false, length = 20)
    private String type;
    
    /**
     * 资源路径
     */
    @Column(name = "resource_path", length = 200)
    private String resourcePath;
    
    /**
     * HTTP方法
     */
    @Column(name = "http_method", length = 10)
    private String httpMethod;
    
    /**
     * 父权限ID
     */
    @Column(name = "parent_id")
    private Long parentId;
    
    /**
     * 权限层级
     */
    @Column(name = "level")
    private Integer level = 1;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 权限状态：ACTIVE, INACTIVE
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status = "ACTIVE";
    
    /**
     * 是否系统内置权限
     */
    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * 角色权限关联
     */
    @OneToMany(mappedBy = "permission", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RolePermission> rolePermissions;
    
    /**
     * 用户权限关联
     */
    @OneToMany(mappedBy = "permission", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserPermission> userPermissions;
    
    /**
     * 子权限
     */
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    private List<Permission> children;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
