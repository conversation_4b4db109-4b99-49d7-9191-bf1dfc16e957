package com.enterprise.bi.user.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户实体类
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Data
@Entity
@Table(name = "users")
@EqualsAndHashCode(callSuper = false)
public class User implements UserDetails {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户名
     */
    @Column(name = "username", nullable = false, unique = true, length = 50)
    private String username;
    
    /**
     * 密码
     */
    @Column(name = "password", nullable = false, length = 255)
    private String password;
    
    /**
     * 真实姓名
     */
    @Column(name = "real_name", length = 100)
    private String realName;
    
    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    private String email;
    
    /**
     * 手机号
     */
    @Column(name = "phone", length = 20)
    private String phone;
    
    /**
     * 部门
     */
    @Column(name = "department", length = 100)
    private String department;
    
    /**
     * 职位
     */
    @Column(name = "position", length = 100)
    private String position;
    
    /**
     * 用户状态：ACTIVE, INACTIVE, LOCKED
     */
    @Column(name = "status", nullable = false, length = 20)
    private String status = "ACTIVE";
    
    /**
     * 用户角色：ADMIN, USER, READONLY
     */
    @Column(name = "role", nullable = false, length = 50)
    private String role = "USER";
    
    /**
     * 头像URL
     */
    @Column(name = "avatar_url", length = 500)
    private String avatarUrl;
    
    /**
     * 最后登录时间
     */
    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 创建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * 用户角色关联
     */
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserRole> userRoles;
    
    /**
     * 用户权限关联
     */
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserPermission> userPermissions;
    
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Spring Security UserDetails 接口实现
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 基于角色和权限构建权限列表
        List<GrantedAuthority> authorities = List.of(new SimpleGrantedAuthority("ROLE_" + role));
        
        if (userPermissions != null) {
            List<GrantedAuthority> permissionAuthorities = userPermissions.stream()
                    .map(up -> new SimpleGrantedAuthority(up.getPermission().getCode()))
                    .collect(Collectors.toList());
            authorities.addAll(permissionAuthorities);
        }
        
        return authorities;
    }
    
    @Override
    public String getPassword() {
        return password;
    }
    
    @Override
    public String getUsername() {
        return username;
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return !"LOCKED".equals(status);
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }
    
    @Override
    public boolean isEnabled() {
        return "ACTIVE".equals(status);
    }
}
