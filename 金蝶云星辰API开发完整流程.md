# 金蝶云星辰API开发完整流程

## 目录
1. [环境准备](#环境准备)
2. [API网关鉴权配置](#api网关鉴权配置)
3. [项目结构说明](#项目结构说明)
4. [核心组件介绍](#核心组件介绍)
5. [API调用流程](#api调用流程)
6. [常用API接口](#常用api接口)
7. [错误处理](#错误处理)
8. [最佳实践](#最佳实践)
9. [故障排除](#故障排除)

## 环境准备

### 1. 基础环境要求
- Java 8+ (推荐Java 11或更高版本)
- Maven 3.6+
- Spring Boot 2.7+
- 金蝶云星辰开发者账号

### 2. 依赖库准备
```xml
<!-- 项目依赖 -->
<dependencies>
    <!-- Spring Boot Starter Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- FastJSON -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
    </dependency>
    
    <!-- Lombok -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <optional>true</optional>
    </dependency>
</dependencies>

<!-- 系统依赖 -->
<dependency>
    <groupId>com.kingdee</groupId>
    <artifactId>kingdee-xw-openapi</artifactId>
    <version>1.0</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/../lib/kingdee-xw-openapi.jar</systemPath>
</dependency>

<dependency>
    <groupId>com.smecloud</groupId>
    <artifactId>apigwclient</artifactId>
    <version>0.1.5</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/../lib/apigwclient-0.1.5.jar</systemPath>
</dependency>
```

### 3. JVM参数配置
```xml
<!-- 解决Java模块系统访问权限问题 -->
<plugin>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-maven-plugin</artifactId>
    <configuration>
        <includeSystemScope>true</includeSystemScope>
        <jvmArguments>--add-opens=java.base/sun.security.action=ALL-UNNAMED</jvmArguments>
    </configuration>
</plugin>
```

## API网关鉴权配置

### 1. 配置文件设置 (application.yml)
```yaml
# 金蝶云星辰API配置
kingdee:
  # 基础配置
  client_id: "你的客户端ID"
  client_secret: "你的客户端密钥"
  app_key: "你的应用Key"
  app_secret: "你的应用密钥"
  third_party_instance_id: "你的第三方实例ID"
  
  # API网关配置
  gateway:
    client_id: "网关客户端ID"
    client_secret: "网关客户端密钥"
    base_url: "https://openapi.kingdee.com"
    auth_path: "/auth/app-token"
  
  # API接口配置
  api:
    base_url: "https://openapi.kingdee.com"
    customer_path: "/api/xw/customer/query"
    product_path: "/api/xw/product/query"

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api

# 日志配置
logging:
  level:
    com.kingdee.linkxc: DEBUG
    root: INFO
```

### 2. 获取配置参数
登录金蝶云星辰开发者平台：
1. 访问 [金蝶云星辰开放平台](https://open.kingdee.com)
2. 创建应用获取 `app_key` 和 `app_secret`
3. 配置API网关获取 `gateway.client_id` 和 `gateway.client_secret`
4. 获取 `third_party_instance_id`

## 项目结构说明

```
backend/
├── src/main/java/com/kingdee/linkxc/
│   ├── LinkXcBackendApplication.java     # 主启动类
│   ├── config/
│   │   └── ClientApiConfig.java          # API客户端配置
│   ├── controller/
│   │   ├── AuthController.java           # 鉴权控制器
│   │   ├── CustomerController.java       # 客户管理控制器
│   │   └── ProductController.java        # 商品管理控制器
│   ├── service/
│   │   ├── AuthService.java              # 鉴权服务
│   │   ├── CustomerService.java          # 客户服务
│   │   └── ProductService.java           # 商品服务
│   ├── util/
│   │   ├── ApiGatewayClient.java         # API网关客户端
│   │   └── KingdeeApiClient.java         # 金蝶API客户端
│   └── model/
│       ├── Customer.java                 # 客户实体
│       └── Product.java                  # 商品实体
└── src/main/resources/
    └── application.yml                   # 配置文件
```

## 核心组件介绍

### 1. API网关客户端 (ApiGatewayClient)
负责通过API网关进行鉴权，获取访问令牌：

```java
@Component
public class ApiGatewayClient {
    // 使用反射机制调用API网关SDK
    // 避免直接依赖，解决编译问题
    
    public Map<String, Object> getKingdeeAuthToken(String uid) {
        // 1. 构建鉴权请求
        // 2. 调用API网关
        // 3. 解析响应结果
        // 4. 返回Token信息
    }
    
    // 通用API网关请求方法
    public Object sendRequest(Object httpMethod, String host, String path, 
                            Map<String, String> queryParams, 
                            Map<String, String> headers,
                            byte[] body) throws Exception
}
```

### 2. 金蝶API客户端 (KingdeeApiClient)
封装金蝶云星辰API调用逻辑：

```java
@Component
public class KingdeeApiClient {
    // 支持两种鉴权方式：
    // 1. 直接API鉴权
    // 2. API网关鉴权（推荐）
    
    public String getAppTokenWithGateway() {
        // 通过API网关获取Token
    }
    
    public String getAppTokenDirect() {
        // 直接调用金蝶API获取Token
    }
    
    // 执行业务单据查询
    public Map<String, Object> executeBillQuery(String formId, Map<String, Object> data)
}
```

### 3. Token缓存服务 (TokenCacheService)
**新增组件** - 管理Token缓存，提升性能：

```java
@Service
public class TokenCacheService {
    // Token缓存管理
    private final Map<String, TokenInfo> tokenCache = new ConcurrentHashMap<>();
    
    // 存储Token到缓存（23小时有效期）
    public void putToken(String key, String token, long ttlMillis)
    
    // 从缓存获取Token
    public String getToken(String key)
    
    // 检查Token是否存在且有效
    public boolean hasToken(String key)
    
    // 清除指定Token
    public void removeToken(String key)
}
```

### 4. 鉴权服务 (AuthService)
**重构升级** - 集成缓存机制，支持多种鉴权方式：

```java
@Service
public class AuthService {
    // 通过API网关获取Token（推荐，支持缓存）
    public String getAppTokenWithGateway()
    
    // 直接获取Token（备用方式）
    public String getAppToken()
    
    // 获取完整授权信息
    public Map<String, Object> getAuthorizeInfo()
    
    // 清除缓存的Token
    public void clearToken()
}
```

### 5. 业务服务层
- **CustomerService**: 客户信息管理
- **MaterialService**: 商品/物料信息管理

## API调用流程

### 1. 完整调用时序图
```
客户端 -> Controller -> Service -> KingdeeApiClient -> ApiGatewayClient -> 金蝶API
   |         |           |              |                    |              |
   |         |           |              |                    |              |
   |         |           |              |                    |<-------------|
   |         |           |              |<-------------------|              |
   |         |           |<-------------|                                   |
   |         |<----------|                                                  |
   |<--------|                                                              |
```

### 2. 详细步骤说明

#### 步骤1: 获取访问令牌
```java
// 1. 调用API网关鉴权
GET /api/auth/access-token-gateway

// 2. 系统内部流程：
AuthController -> AuthService -> KingdeeApiClient -> ApiGatewayClient
```

#### 步骤2: 调用业务API
```java
// 1. 获取客户信息
GET /api/customers

// 2. 获取商品信息  
GET /api/products

// 3. 系统内部流程：
Controller -> Service -> KingdeeApiClient (使用已获取的Token)
```

### 3. 核心代码示例

#### 鉴权流程
```java
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @GetMapping("/access-token-gateway")
    public ResponseEntity<Map<String, Object>> getAccessTokenWithGateway() {
        try {
            // 通过API网关获取Token
            String appToken = authService.getAppTokenWithGateway();
            
            Map<String, Object> response = new HashMap<>();
            response.put("method", "API Gateway");
            response.put("app_token", appToken);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(Map.of("error", "获取访问凭证失败: " + e.getMessage()));
        }
    }
}
```

#### 业务API调用
```java
@RestController
@RequestMapping("/customers")
public class CustomerController {
    
    @GetMapping
    public ResponseEntity<List<Customer>> getCustomers() {
        try {
            // 1. 获取Token
            String token = kingdeeApiClient.getAppTokenWithGateway();
            
            // 2. 调用客户查询API
            List<Customer> customers = customerService.getCustomers(token);
            
            return ResponseEntity.ok(customers);
        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(Collections.emptyList());
        }
    }
}
```

## 常用API接口

### 1. 鉴权接口
| 接口 | 方法 | 说明 | 缓存支持 |
|------|------|------|----------|
| `/api/auth/auth-info` | GET | 获取授权信息（推荐） | ✅ 23小时缓存 |
| `/api/auth/access-token-gateway` | GET | API网关鉴权 | ❌ |
| `/api/auth/access-token-direct` | GET | 直接API鉴权 | ❌ |

### 2. 业务接口
| 接口 | 方法 | 说明 | 状态 |
|------|------|------|------|
| `/api/customer/list` | GET | 获取客户列表 | ✅ 可用 |
| `/api/material/list` | GET | 获取商品/物料列表 | ✅ 可用 |

### 3. 缓存管理接口
| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/auth/clear-cache` | POST | 清除Token缓存 |

### 3. 金蝶云星辰原生API
| API路径 | 说明 | 参数 |
|---------|------|------|
| `/auth/app-token` | 获取应用Token | app_key, app_signature |
| `/api/xw/customer/query` | 查询客户信息 | access_token, 查询条件 |
| `/api/xw/product/query` | 查询商品信息 | access_token, 查询条件 |

## 错误处理

### 1. 常见错误码
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 401 | 未授权 | 检查Token是否有效 |
| 403 | 禁止访问 | 检查权限配置 |
| 500 | 服务器错误 | 检查服务状态和日志 |

### 2. 异常处理策略
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        Map<String, Object> error = new HashMap<>();
        error.put("error", e.getMessage());
        error.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(500).body(error);
    }
}
```

### 3. 重试机制
```java
@Retryable(value = {Exception.class}, maxAttempts = 3)
public String getAppToken() {
    // API调用逻辑
}
```

## Token缓存机制

### 1. 缓存策略
- **缓存时间**: 23小时（Token有效期24小时，提前1小时刷新）
- **缓存键格式**: `gateway_token_default`
- **存储方式**: 内存缓存（ConcurrentHashMap）
- **自动过期**: 支持TTL自动清理

### 2. 缓存流程
```java
// 1. 检查缓存
if (tokenCacheService.hasToken("gateway_token_default")) {
    return tokenCacheService.getToken("gateway_token_default");
}

// 2. 获取新Token
String newToken = apiGatewayClient.getKingdeeAuthToken(null);

// 3. 存储到缓存（23小时）
tokenCacheService.putToken("gateway_token_default", newToken, 23 * 60 * 60 * 1000);

return newToken;
```

### 3. 缓存优势
- **性能提升**: 避免频繁的鉴权请求
- **稳定性**: 减少对金蝶API的依赖
- **成本控制**: 降低API调用次数

## 最佳实践

### 1. Token管理
- ✅ 实现Token缓存机制（已完成）
- ✅ 监控Token过期时间（已完成）
- ✅ 自动刷新Token（已完成）
- 支持手动清除缓存

### 2. 安全性
- 不在日志中输出敏感信息
- 使用HTTPS进行API调用
- 定期更新密钥

### 3. 性能优化
- 使用连接池
- 实现请求缓存
- 异步处理非关键请求

### 4. 监控和日志
```java
// 详细的日志记录
log.info("开始调用金蝶API - 接口: {}, 参数: {}", apiPath, params);
log.debug("API响应: {}", response);
log.error("API调用失败: {}", e.getMessage(), e);
```

## 故障排除

### 1. 启动问题
**问题**: 应用启动失败，模块访问权限错误
```
IllegalAccessError: class com.smecloud.apigw.codec.GwURLEncoder cannot access class sun.security.action.GetPropertyAction
```

**解决方案**: 添加JVM参数
```xml
<jvmArguments>--add-opens=java.base/sun.security.action=ALL-UNNAMED</jvmArguments>
```

### 2. API调用问题
**问题**: NoSuchMethodException
```
java.lang.NoSuchMethodException: com.smecloud.apigw.model.ApiResult.getCode()
```

**解决方案**: 检查方法名，使用正确的方法
```java
// 错误
Method getCode = apiResultClass.getMethod("getCode");

// 正确  
Method getHttpCode = apiResultClass.getMethod("getHttpCode");
```

### 3. 配置问题
**问题**: 配置参数错误导致鉴权失败

**解决方案**: 
1. 检查配置文件中的参数是否正确
2. 验证金蝶开放平台的应用配置
3. 确认API网关配置

### 4. 网络问题
**问题**: 连接超时或网络不可达

**解决方案**:
1. 检查网络连接
2. 配置代理设置
3. 增加超时时间

## 快速开始

### 1. 克隆项目
```bash
git clone <项目地址>
cd Link-XC
```

### 2. 配置参数
编辑 `backend/src/main/resources/application.yml`，填入你的配置参数。

### 3. 启动服务
```bash
cd backend
mvn spring-boot:run
```

### 4. 测试API
```bash
# 测试API网关鉴权
curl http://localhost:8080/api/auth/access-token-gateway

# 测试获取客户信息
curl http://localhost:8080/api/customers

# 测试获取商品信息
curl http://localhost:8080/api/products
```

## 总结

本文档提供了金蝶云星辰API开发的完整流程，包括：
- 环境准备和配置
- API网关鉴权实现
- 业务API调用
- 错误处理和故障排除

通过遵循本文档的指导，您可以快速构建稳定可靠的金蝶云星辰API集成应用。

---
*最后更新时间: 2025年9月*