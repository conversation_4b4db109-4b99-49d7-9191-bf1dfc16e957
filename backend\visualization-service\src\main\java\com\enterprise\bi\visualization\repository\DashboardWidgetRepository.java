package com.enterprise.bi.visualization.repository;

import com.enterprise.bi.visualization.entity.DashboardWidget;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 仪表板组件仓库接口
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Repository
public interface DashboardWidgetRepository extends JpaRepository<DashboardWidget, Long> {
    
    /**
     * 根据仪表板ID查找组件，按排序顺序
     */
    List<DashboardWidget> findByDashboardIdOrderBySortOrder(Long dashboardId);
    
    /**
     * 根据仪表板ID查找启用的组件
     */
    List<DashboardWidget> findByDashboardIdAndIsEnabledTrue(Long dashboardId);
    
    /**
     * 根据组件类型查找组件
     */
    List<DashboardWidget> findByWidgetType(String widgetType);
    
    /**
     * 根据图表ID查找组件
     */
    List<DashboardWidget> findByChartId(Long chartId);
    
    /**
     * 根据仪表板ID删除组件
     */
    void deleteByDashboardId(Long dashboardId);
    
    /**
     * 根据仪表板ID统计组件数量
     */
    long countByDashboardId(Long dashboardId);
    
    /**
     * 根据组件类型统计数量
     */
    long countByWidgetType(String widgetType);
    
    /**
     * 统计各组件类型数量
     */
    @Query("SELECT w.widgetType, COUNT(w) FROM DashboardWidget w GROUP BY w.widgetType")
    List<Object[]> countWidgetsByType();
    
    /**
     * 查找指定仪表板中的图表组件
     */
    @Query("SELECT w FROM DashboardWidget w WHERE w.dashboardId = :dashboardId AND w.widgetType = 'CHART'")
    List<DashboardWidget> findChartWidgetsByDashboardId(@Param("dashboardId") Long dashboardId);
    
    /**
     * 查找使用指定图表的所有组件
     */
    List<DashboardWidget> findByChartIdAndIsEnabledTrue(Long chartId);
    
    /**
     * 根据创建者查找组件
     */
    List<DashboardWidget> findByCreatedBy(String createdBy);
    
    /**
     * 查找指定位置范围内的组件
     */
    @Query("SELECT w FROM DashboardWidget w WHERE w.dashboardId = :dashboardId " +
           "AND w.positionX >= :minX AND w.positionX <= :maxX " +
           "AND w.positionY >= :minY AND w.positionY <= :maxY")
    List<DashboardWidget> findWidgetsInArea(@Param("dashboardId") Long dashboardId,
                                           @Param("minX") Integer minX, @Param("maxX") Integer maxX,
                                           @Param("minY") Integer minY, @Param("maxY") Integer maxY);
}
