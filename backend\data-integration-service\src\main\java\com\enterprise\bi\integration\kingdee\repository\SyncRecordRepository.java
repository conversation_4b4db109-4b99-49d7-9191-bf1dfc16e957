package com.enterprise.bi.integration.kingdee.repository;

import com.enterprise.bi.integration.kingdee.entity.SyncRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 同步记录仓库
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Repository
public interface SyncRecordRepository extends JpaRepository<SyncRecord, Long> {
    
    /**
     * 根据任务ID查找记录
     */
    List<SyncRecord> findByTaskId(Long taskId);
    
    /**
     * 根据任务ID分页查找记录
     */
    Page<SyncRecord> findByTaskId(Long taskId, Pageable pageable);
    
    /**
     * 根据状态查找记录
     */
    List<SyncRecord> findByStatus(String status);
    
    /**
     * 根据数据类型查找记录
     */
    List<SyncRecord> findByDataType(String dataType);
    
    /**
     * 根据时间范围查找记录
     */
    List<SyncRecord> findByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找任务的最新记录
     */
    Optional<SyncRecord> findTopByTaskIdOrderByStartTimeDesc(Long taskId);
    
    /**
     * 查找任务的最新成功记录
     */
    Optional<SyncRecord> findTopByTaskIdAndStatusOrderByStartTimeDesc(Long taskId, String status);
    
    /**
     * 统计各状态的记录数量
     */
    @Query("SELECT r.status, COUNT(r) FROM SyncRecord r GROUP BY r.status")
    List<Object[]> countByStatus();
    
    /**
     * 统计各数据类型的记录数量
     */
    @Query("SELECT r.dataType, COUNT(r) FROM SyncRecord r GROUP BY r.dataType")
    List<Object[]> countByDataType();
    
    /**
     * 统计指定时间范围内的同步数量
     */
    @Query("SELECT SUM(r.syncCount) FROM SyncRecord r WHERE r.status = 'SUCCESS' " +
           "AND r.startTime BETWEEN :startTime AND :endTime")
    Long sumSyncCountByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找运行中的记录
     */
    List<SyncRecord> findByStatusAndStartTimeBefore(String status, LocalDateTime beforeTime);
    
    /**
     * 删除指定时间之前的记录
     */
    void deleteByCreatedAtBefore(LocalDateTime beforeTime);
    
    /**
     * 查找失败的记录
     */
    @Query("SELECT r FROM SyncRecord r WHERE r.status = 'FAILED' " +
           "AND r.startTime >= :startTime ORDER BY r.startTime DESC")
    List<SyncRecord> findFailedRecordsSince(@Param("startTime") LocalDateTime startTime);
    
    /**
     * 统计任务的成功率
     */
    @Query("SELECT r.taskId, " +
           "COUNT(CASE WHEN r.status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(r) as successRate " +
           "FROM SyncRecord r WHERE r.startTime >= :startTime " +
           "GROUP BY r.taskId")
    List<Object[]> calculateSuccessRateSince(@Param("startTime") LocalDateTime startTime);
}
