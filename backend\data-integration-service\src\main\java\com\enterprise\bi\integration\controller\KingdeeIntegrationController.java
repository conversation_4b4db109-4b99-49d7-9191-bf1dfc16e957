package com.enterprise.bi.integration.controller;

import com.enterprise.bi.integration.kingdee.entity.SyncTask;
import com.enterprise.bi.integration.kingdee.entity.SyncRecord;
import com.enterprise.bi.integration.kingdee.service.KingdeeDataSyncService;
import com.enterprise.bi.integration.kingdee.repository.SyncTaskRepository;
import com.enterprise.bi.integration.kingdee.repository.SyncRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 金蝶集成控制器
 * 
 * <AUTHOR> BI Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/kingdee")
@RequiredArgsConstructor
public class KingdeeIntegrationController {
    
    private final KingdeeDataSyncService syncService;
    private final SyncTaskRepository syncTaskRepository;
    private final SyncRecordRepository syncRecordRepository;
    
    /**
     * 获取所有同步任务
     */
    @GetMapping("/tasks")
    public ResponseEntity<List<SyncTask>> getAllTasks() {
        List<SyncTask> tasks = syncTaskRepository.findAll();
        return ResponseEntity.ok(tasks);
    }
    
    /**
     * 根据ID获取同步任务
     */
    @GetMapping("/tasks/{id}")
    public ResponseEntity<SyncTask> getTaskById(@PathVariable Long id) {
        return syncTaskRepository.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 创建同步任务
     */
    @PostMapping("/tasks")
    public ResponseEntity<SyncTask> createTask(@Valid @RequestBody SyncTask task) {
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        SyncTask savedTask = syncTaskRepository.save(task);
        return ResponseEntity.ok(savedTask);
    }
    
    /**
     * 更新同步任务
     */
    @PutMapping("/tasks/{id}")
    public ResponseEntity<SyncTask> updateTask(@PathVariable Long id, @Valid @RequestBody SyncTask task) {
        return syncTaskRepository.findById(id)
                .map(existingTask -> {
                    existingTask.setName(task.getName());
                    existingTask.setDescription(task.getDescription());
                    existingTask.setDataType(task.getDataType());
                    existingTask.setSyncConfig(task.getSyncConfig());
                    existingTask.setStatus(task.getStatus());
                    existingTask.setEnabled(task.getEnabled());
                    existingTask.setSyncInterval(task.getSyncInterval());
                    existingTask.setUpdatedAt(LocalDateTime.now());
                    return ResponseEntity.ok(syncTaskRepository.save(existingTask));
                })
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 删除同步任务
     */
    @DeleteMapping("/tasks/{id}")
    public ResponseEntity<Void> deleteTask(@PathVariable Long id) {
        if (syncTaskRepository.existsById(id)) {
            syncTaskRepository.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
    
    /**
     * 启用/禁用同步任务
     */
    @PutMapping("/tasks/{id}/toggle")
    public ResponseEntity<SyncTask> toggleTask(@PathVariable Long id) {
        return syncTaskRepository.findById(id)
                .map(task -> {
                    task.setEnabled(!task.getEnabled());
                    task.setUpdatedAt(LocalDateTime.now());
                    return ResponseEntity.ok(syncTaskRepository.save(task));
                })
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 手动执行同步任务
     */
    @PostMapping("/tasks/{id}/execute")
    public ResponseEntity<Map<String, Object>> executeTask(@PathVariable Long id) {
        try {
            CompletableFuture<Void> future = syncService.executeSyncAsync(id);
            return ResponseEntity.ok(Map.of(
                "message", "同步任务已启动",
                "taskId", id,
                "timestamp", LocalDateTime.now()
            ));
        } catch (Exception e) {
            log.error("执行同步任务失败", e);
            return ResponseEntity.badRequest().body(Map.of(
                "error", "执行同步任务失败: " + e.getMessage(),
                "taskId", id,
                "timestamp", LocalDateTime.now()
            ));
        }
    }
    
    /**
     * 获取同步记录
     */
    @GetMapping("/records")
    public ResponseEntity<Page<SyncRecord>> getRecords(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long taskId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String dataType) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("startTime").descending());
        
        Page<SyncRecord> records;
        if (taskId != null) {
            records = syncRecordRepository.findByTaskId(taskId, pageable);
        } else {
            records = syncRecordRepository.findAll(pageable);
        }
        
        return ResponseEntity.ok(records);
    }
    
    /**
     * 获取同步统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        List<Object[]> taskStatusStats = syncTaskRepository.countByStatus();
        List<Object[]> recordStatusStats = syncRecordRepository.countByStatus();
        List<Object[]> dataTypeStats = syncTaskRepository.countByDataType();
        
        // 计算今日同步数量
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
        Long todaySyncCount = syncRecordRepository.sumSyncCountByTimeRange(todayStart, todayEnd);
        
        Map<String, Object> statistics = Map.of(
            "taskStatusStats", taskStatusStats,
            "recordStatusStats", recordStatusStats,
            "dataTypeStats", dataTypeStats,
            "todaySyncCount", todaySyncCount != null ? todaySyncCount : 0,
            "totalTasks", syncTaskRepository.count(),
            "totalRecords", syncRecordRepository.count()
        );
        
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * 获取任务的最新同步记录
     */
    @GetMapping("/tasks/{id}/latest-record")
    public ResponseEntity<SyncRecord> getLatestRecord(@PathVariable Long id) {
        return syncRecordRepository.findTopByTaskIdOrderByStartTimeDesc(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 获取系统健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealth() {
        long activeTasks = syncTaskRepository.findByStatusAndEnabled("ACTIVE", true).size();
        long runningRecords = syncRecordRepository.findByStatus("RUNNING").size();
        
        Map<String, Object> health = Map.of(
            "status", "UP",
            "activeTasks", activeTasks,
            "runningRecords", runningRecords,
            "timestamp", LocalDateTime.now()
        );
        
        return ResponseEntity.ok(health);
    }
    
    /**
     * 测试金蝶连接
     */
    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, Object>> testConnection() {
        try {
            // 这里可以添加实际的连接测试逻辑
            Map<String, Object> result = Map.of(
                "status", "SUCCESS",
                "message", "连接测试成功",
                "timestamp", LocalDateTime.now()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            Map<String, Object> result = Map.of(
                "status", "FAILED",
                "message", "连接测试失败: " + e.getMessage(),
                "timestamp", LocalDateTime.now()
            );
            return ResponseEntity.badRequest().body(result);
        }
    }
}
